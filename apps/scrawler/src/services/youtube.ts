import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import { Channel } from '@/api/@types/rapidapi/Youtube.ts'
import RapidApiStatsCollector from '@/api/ApiCallStat.ts'
import { logFilter } from '@/api/util'
import YoutubeApi from '@/api/youtube.ts'
import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import {
  CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS,
  LONG_CRAWLER_YTB_BATCH_SIZE,
  YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS,
  YOUTUBE_FETCH_CHANNEL_CONCURRENCY,
  YOUTUBE_RELATED_VIDEO_COUNT,
  YOUTUBE_USER_VIDEO_COUNT,
} from '@/config/env.ts'
import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import { EmailFields, createYtbKolByIdOrName } from '@/services/kolInfo.service'
import { PublicationStatsService } from '@/services/publicationStats.service'
import { findCurrentTasksByProjectId } from '@/services/similar.ts'
import { generateEmbeddingsForYoutube, getEmbedding } from '@/services/worker/embedding.ts'
import { YoutubeHashTagBreakResult } from '@/types/breakResult'
import { EmailSourceType } from '@/types/email'
import { DeductDynamicQuotaParams } from '@/types/memberShip'
import { ProjectCandidate, ProjectConfig } from '@/types/project'
import {
  SimilarTaskParams,
  YoutubeHashTagBreakTaskParams,
  YoutubeHashtagBreakTaskResult,
  YoutubeSearchInputBreakTaskParams,
  YoutubeSearchInputBreakTaskResult,
  YtbLongCrawlerBatchLogs,
  YtbLongCrawlerFilters,
  YtbLongCrawlerProgress,
  YtbLongCrawlerTaskParams,
} from '@/types/task.ts'
import { YoutubeTaskResult } from '@/types/task/youtubeTask'
import { GetTaskParams } from '@/types/taskParams'
import {
  PriorityQueueFactory,
  YoutubePriorityQueue,
  YoutubePriorityUser,
  calculateUserPriority,
} from '@/utils/PriorityQueue'
import { getUnixTimestamp, parsePublishedTime } from '@/utils/date.ts'
import {
  KolInfo,
  KolPlatform,
  Prisma,
  ProjectKolAttitude,
  QuotaType,
  SimilarChannelTaskStatus,
  TaskType,
  YouTubeChannel,
  prisma,
} from '@repo/database'
import Bluebird from 'bluebird'
import Bull from 'bull'
import dayjs from 'dayjs'
import { BaseChannel } from 'youtubei'
import {
  YouTubeChannelSimilarityOutput,
  analyzeVisualSimilarityService,
} from './aiTools/visualSimilarity'
import EmailService from './email'
import { getExcludeIds } from './excludeList'
import { MembershipService } from './membership.service'
import {
  EnhancedMilvusData,
  advancedFilterSearch,
  bulkInsertEnhancedWithDelete,
  getDenseVectorsByUserId,
} from './milvus.service'
import { ProjectKolService } from './projectKol.service'

const HASHTAG_DEFAULT_LANG = 'en'
const HASHTAG_DEFAULT_GEO = 'US'

class YoutubeService {
  private static instance: YoutubeService

  static getInstance(): YoutubeService {
    if (!YoutubeService.instance) {
      YoutubeService.instance = new YoutubeService()
    }
    return YoutubeService.instance
  }

  public async createKolInfosForChannels(
    userId: string | undefined,
    channelIds: string[],
  ): Promise<KolInfo[]> {
    if (!channelIds || channelIds.length === 0) {
      throw new Error('没有提供频道 ID')
    }

    // // 查询数据库中已经存在的记录
    // const existingKolInfos = await prisma.kolInfo.findMany({
    //   where: {
    //     platformAccount: {
    //       in: channelIds,
    //     },
    //     platform: KolPlatform.YOUTUBE,
    //   },
    // })

    // // 获取已经存在的 channelId
    // const existingChannelIds = existingKolInfos.map((kolInfo) => kolInfo.platformAccount)

    // // 过滤出需要创建的 channelIds
    // const channelIdsToCreate = channelIds.filter(
    //   (channelId) => !existingChannelIds.includes(channelId),
    // )

    const createdKolInfos = await Bluebird.map(
      channelIds,
      async (channelId) => {
        try {
          const kolInfo = await createYtbKolByIdOrName(userId, channelId)
          if (kolInfo) {
            console.log(`已为 channelId: ${channelId} 创建 KOL 信息`)
            return kolInfo
          } else {
            console.warn(`为 channelId: ${channelId} 创建 KOL 信息失败`)
            return null
          }
        } catch (error) {
          console.error(`为 channelId: ${channelId} 创建 KOL 信息时出错`, error)
          Sentry.captureException(error)
          return null
        }
      },
      { concurrency: 30 },
    )

    // 合并新创建的 KOL 和已经存在的 KOL 信息，过滤掉 null 值
    return createdKolInfos.filter(Boolean) as KolInfo[]
  }

  public async superlikeSourceChannel(params: SimilarTaskParams, taskId: string): Promise<void> {
    if (params?.source && params.projectId) {
      const matchingKol = await prisma.kolInfo.findFirst({
        where: {
          platformAccount: params.source,
        },
      })

      if (matchingKol) {
        await prisma.projectKol.upsert({
          where: {
            projectId_kolId: {
              projectId: params.projectId,
              kolId: matchingKol.id,
            },
          },
          update: {
            attitude: ProjectKolAttitude.SUPERLIKE,
            lastSimilarAt: new Date(),
            similarTaskId: taskId,
          },
          create: {
            projectId: params.projectId,
            kolId: matchingKol.id,
            similarTaskId: taskId,
            attitude: ProjectKolAttitude.SUPERLIKE,
            rateBy: 'system',
            lastSimilarAt: new Date(),
          },
        })
      }
    }
  }

  public async createKolById(
    channelId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<KolInfo> {
    const exist = await prisma.kolInfo.findFirst({
      where: {
        platformAccount: channelId,
        platform: KolPlatform.YOUTUBE,
      },
    })
    if (exist) {
      return exist
    }
    const channel = await YoutubeApi.getInstance().getChannelWithVideos(channelId, collector)
    if (!channel) {
      throw new Error('channel not found: ' + channelId)
    }
    return prisma.kolInfo.create({
      data: {
        title: channel.title,
        description: channel.description,
        links: channel.links
          ? channel.links.map((i) => (i.link.startsWith('https://') ? i.link : `https://${i.link}`))
          : [],
        email: channel.email,
        historyEmails: channel.email ? [channel.email] : [],
        platformAccount: channel.channelId,
        platform: KolPlatform.YOUTUBE,
        avatar: channel?.avatar[0]?.url ?? '',
        emailUpdatedAt: new Date(),
      },
    })
  }

  public async youtubeProcessHashTagBreakJob(
    taskParams: YoutubeHashTagBreakTaskParams,
    taskId: string,
  ): Promise<YoutubeHashtagBreakTaskResult> {
    console.log(`[instagramProcessHashTagBreakJob] 开始处理标签 ${taskParams.tag}`)
    const collector = new RapidApiStatsCollector()
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的 YouTube 用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.YOUTUBE],
      })
      console.log(
        `[youtubeProcessHashTagBreakJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的频道`,
      )

      let previousCandidatesChannelIds: string[] = []
      if (taskParams.paginationToken) {
        const previousKolUniqueIds = await prisma.projectCandidate.findUnique({
          where: {
            projectId_type: {
              projectId: taskParams.projectId,
              type: TaskType.HASH_TAG_BREAK,
            },
          },
          select: {
            candidates: true,
          },
        })
        const candidates = previousKolUniqueIds?.candidates as unknown as {
          kols: ProjectCandidate[]
        }
        if (candidates && candidates.kols) {
          previousCandidatesChannelIds = candidates.kols.map((candidate) => candidate.platformId)
          console.log(
            `[youtubeProcessHashTagBreakJob] 获取到 ${previousCandidatesChannelIds.length} 个当前进行的流已经爬取到的频道`,
          )
        }
      }
      const ratedChannelIds = [...projectKolUniques, ...previousCandidatesChannelIds]
      const options = {
        ratedChannelIds: ratedChannelIds || [],
      }

      const result: YoutubeHashTagBreakResult = await this.getChannelsFromHashtag(
        taskParams.tag,
        taskParams.paginationToken,
        taskParams.maxVideoCount ?? 100,
        taskParams.currentVideoCount ?? 0,
        options,
        collector,
      )
      const { channelIds } = result
      const channels = await Bluebird.map(
        channelIds,
        async (channelId) => {
          try {
            return await YoutubeApi.getInstance().getChannelWithVideos(channelId, collector)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +YOUTUBE_FETCH_CHANNEL_CONCURRENCY },
      )

      let validChannels = channels.filter((channel) => channel !== null)
      // 过滤 1000 粉丝以上
      validChannels = logFilter(validChannels, (channel) => channel.subscriberCount > 1000)
      // 处理kolInfo以及insUserInfo数据
      await Promise.all([
        this.upsertKolInfo(task.createdBy!, validChannels),
        this.processAndUpsertChannels(validChannels),
      ])

      const response: YoutubeHashtagBreakTaskResult = {
        taskId: taskId,
        tag: taskParams.tag,
        channelIds: result.channelIds,
        paginationToken: result.paginationToken,
        hasMore: result.hasMore,
        total: result.total,
        progress: result.progress,
      }

      console.log(
        `[youtubeProcessHashTagBreakJob] 处理完成，共处理 ${result.channelIds.length} 个频道`,
      )
      return response
    } catch (error) {
      console.error(`[youtubeProcessHashTagBreakJob] 处理标签 ${taskParams.tag} 时出错:`, error)
      throw error
    }
  }

  public async youtubeProcessSearchInputBreakJob(
    taskParams: YoutubeSearchInputBreakTaskParams,
    taskId: string,
  ): Promise<YoutubeSearchInputBreakTaskResult> {
    console.log(
      `[youtubeProcessSearchInputBreakJob] 开始处理搜索词 ${taskParams.searchInput}, 使用 token ${taskParams.paginationToken}`,
    )
    const collector = new RapidApiStatsCollector()
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的 YouTube 用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.YOUTUBE],
      })
      console.log(
        `[youtubeProcessHashTagBreakJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的频道`,
      )

      const previousKolUniqueIds = await prisma.projectCandidate.findUnique({
        where: {
          projectId_type: {
            projectId: taskParams.projectId,
            type: TaskType.SEARCH_INPUT_BREAK,
          },
        },
        select: {
          candidates: true,
        },
      })
      const candidates = previousKolUniqueIds?.candidates as unknown as {
        kols: ProjectCandidate[]
      }
      if (candidates && candidates.kols) {
        console.log(
          `[youtubeProcessSearchInputBreakJob] 获取到: ${candidates.kols.length} 个当前进行的流已经爬取到的频道`,
        )
        const platformIds = candidates.kols.map((candidate) => candidate.platformId)
        projectKolUniques.push(...platformIds)
      }

      const options = {
        ratedChannelIds: projectKolUniques || [],
      }
      const queryParams = {
        geo: taskParams.geo,
        lang: taskParams.lang,
        duration: taskParams.duration,
        sort_by: taskParams.sort_by,
        upload_date: taskParams.upload_date,
      }
      const result: YoutubeHashTagBreakResult = await this.getChannelsFromSearch(
        taskParams.searchInput,
        taskParams.paginationToken,
        taskParams.maxVideoCount ?? 100,
        taskParams.currentVideoCount ?? 0,
        taskParams.total ?? 0,
        options,
        queryParams,
        collector,
      )
      const { channelIds } = result
      const channels = await Bluebird.map(
        channelIds,
        async (channelId) => {
          try {
            return await YoutubeApi.getInstance().getChannelWithVideos(channelId, collector)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +YOUTUBE_FETCH_CHANNEL_CONCURRENCY },
      )

      let validChannels = channels.filter((channel) => channel !== null)
      console.log(`默认条件：过滤 1000 粉丝以下博主`)
      validChannels = logFilter(validChannels, (channel) => channel.subscriberCount > 1000)
      // 处理kolInfo以及 youtube UserInfo数据
      await Promise.all([
        this.upsertKolInfo(task.createdBy!, validChannels),
        this.processAndUpsertChannels(validChannels),
      ])

      const response: YoutubeSearchInputBreakTaskResult = {
        taskId: taskId,
        searchInput: taskParams.searchInput,
        channelIds: result.channelIds,
        paginationToken: result.paginationToken,
        hasMore: !!result.paginationToken,
        total: result.total,
        progress: result.progress,
        geo: taskParams.geo,
        lang: taskParams.lang,
        duration: taskParams.duration,
        sort_by: taskParams.sort_by,
        upload_date: taskParams.upload_date,
      }

      console.log(
        `[youtubeProcessSearchInputBreakJob] 处理完成，共处理 ${result.channelIds.length} 个频道`,
      )
      return response
    } catch (error) {
      console.error(
        `[youtubeProcessSearchInputBreakJob] 处理搜索词 ${taskParams.searchInput} 时出错:`,
        error,
      )
      throw error
    }
  }

  private async getValidDenseVector(
    sourceChannel: Channel,
    ytbVideoIds: string[] | undefined,
    sourceIds: string[],
    collector: RapidApiStatsCollector,
  ): Promise<number[]> {
    // 1. 尝试从视频ID获取向量
    let denseVector: number[] = []

    if (ytbVideoIds && ytbVideoIds.length > 0) {
      try {
        const ytbVideos = await Bluebird.map(
          ytbVideoIds,
          async (videoId) => YoutubeApi.getInstance().getVideo(videoId, collector),
          { concurrency: 10 },
        )

        // 确保过滤后的数组只包含非空VideoDetail对象
        const filteredVideos: VideoDetail[] = ytbVideos.filter(
          (video): video is VideoDetail => !!video,
        )

        if (!filteredVideos?.length) {
          throw new Error('no videos found')
        }

        const allText = filteredVideos
          .flatMap((video) => [video.title || '', video.description || ''])
          .join(' ')

        if (!allText?.length) {
          throw new Error('no concat text found')
        }

        denseVector = await getEmbedding(allText)
      } catch (error) {
        console.log('failed to generate embedding from videos:', error)
        denseVector = await getDenseVectorsByUserId(sourceIds[0], KolPlatform.YOUTUBE)
      }
    } else {
      denseVector = await getDenseVectorsByUserId(sourceIds[0], KolPlatform.YOUTUBE)
    }

    // 2. 验证向量有效性，必要时生成新的
    if (denseVector?.length) {
      console.log(`[youtubeProcessSimilarJob] denseVector is not empty, use denseVector`)
      return denseVector
    }

    console.log(
      `[youtubeProcessSimilarJob] denseVector is empty, generate embeddings for source channel`,
    )
    const embeddings = await generateEmbeddingsForYoutube([sourceChannel])

    if (embeddings?.length) {
      return embeddings[0]
    }

    throw new Error('no valid dense vector for source channel')
  }

  public async youtubeProcessSimilarJob(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>
    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
    const { ytbVideoIds, source, channelId } = job.data.params as SimilarTaskParams
    const collector = new RapidApiStatsCollector()

    const ytbChannelId =
      channelId ?? source ?? (await YoutubeApi.getInstance().getYoutubeChannelId(params.source))

    if (!ytbChannelId) {
      throw new Error('no source id found,task failed')
    }

    //project,candidate,exclude rated channel Ids
    const [projectRatedChannelIds, previousCandidatesChannelIds, excludeChannelIds] =
      await metrics.withPhase('get_project_all_rated_channel_ids', () =>
        Promise.all([
          ProjectKolService.getProjectKolUniques(task.projectId, {
            platform: [KolPlatform.YOUTUBE],
          }),
          this.getPreviousTasksCandidatesChannelIds(task.projectId),
          getExcludeIds(task.createdBy!, KolPlatform.YOUTUBE),
        ]),
      )
    const allRatedChannelIds = [
      ...new Set([
        ...projectRatedChannelIds,
        ...previousCandidatesChannelIds,
        ...excludeChannelIds,
      ]),
    ]
    metrics.set('project_all_rated_channel_count', allRatedChannelIds.length)

    // source channel
    const sourceChannel = await metrics.withPhase('get_source_channel', () =>
      YoutubeApi.getInstance().getChannelWithVideos(ytbChannelId, collector),
    )
    if (!sourceChannel?.videos?.length) {
      throw new Error('no videos found for source channel')
    }

    // related channels
    let relatedChannels: Channel[] = []
    if (!ytbVideoIds?.length) {
      console.log(`[youtubeProcessSimilarJob] get related channels by latest videos`)
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByRapidApi(sourceChannel, params.regions, collector),
      )
    } else {
      console.log(`[youtubeProcessSimilarJob] get related channels by videoIds`)
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByVideoIds(ytbVideoIds, params.regions, collector),
      )
    }

    if (!relatedChannels?.length) {
      throw new Error('no related channels found')
    }
    metrics.set('source_channel_related_channels_count', relatedChannels.length)

    // 确保源频道在列表中，并过滤掉已评价的频道
    const filteredRelatedChannels = relatedChannels.filter(
      (channel) => channel.channelId && !allRatedChannelIds.includes(channel.channelId),
    )
    metrics.set('after_rated_filter_related_channels_count', filteredRelatedChannels.length)

    if (!filteredRelatedChannels.some((c) => c.channelId === sourceChannel.channelId)) {
      filteredRelatedChannels.unshift(sourceChannel)
    }

    //upsert kol info and channel vectors
    await metrics.withPhase('upsert_kol_info_and_channel_vectors', async () =>
      Promise.all([
        this.upsertKolInfo(task.createdBy ?? undefined, filteredRelatedChannels),
        this.upsertChannelInfoAndVectors(filteredRelatedChannels),
      ]),
    )

    const validDenseVector = await metrics.withPhase('get_valid_dense_vector', () =>
      this.getValidDenseVector(sourceChannel, ytbVideoIds, [ytbChannelId], collector),
    )
    // add source channel
    allRatedChannelIds.push(ytbChannelId)
    const filter = {
      taskType: TaskType.SIMILAR,
      platform: KolPlatform.YOUTUBE,
      regions: params.regions,
      minFollowers: params.minSubscribers,
      maxFollowers: params.maxSubscribers,
      minAveragePlayCount: params.videosAverageViews,
      maxAveragePlayCount: params.maxVideosAverageViews,
      lastPublishedAfter: Number(YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS)
        ? Math.floor(Date.now() / 1000) - Number(YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS) * 24 * 60 * 60
        : undefined,
      ratedIds: allRatedChannelIds,
    }

    const vectorSearchResult = await metrics.withPhase('vector_search', () =>
      advancedFilterSearch([validDenseVector], filter),
    )

    if (!vectorSearchResult?.length) {
      return []
    }
    metrics.set('vector_search_result_count', vectorSearchResult.length)

    // Query KOL info with database filters
    const kols = await metrics.withPhase('db_query_kol_info', () =>
      prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.YOUTUBE,
          youtubeChannel: {
            channelId: {
              in: vectorSearchResult.map((i) => i.userId),
            },
          },
        },
        include: {
          youtubeChannel: true,
        },
      }),
    )

    if (!kols?.length) {
      return []
    }

    metrics.set('final_kols_count', kols.length)
    metrics.set('rapid_api_stats', collector.getStats())

    await YoutubeService.getInstance().superlikeSourceChannel(params, id)

    const embeddedChannelIdsSet = new Set(
      filteredRelatedChannels.map((channel) => channel.channelId),
    )
    const channelIdsNeedUpdate = vectorSearchResult
      .map((item) => item.userId)
      .filter((channelId) => !embeddedChannelIdsSet.has(channelId))

    if (channelIdsNeedUpdate.length > 0) {
      await this.updateMilvusChannelsWithVideos(channelIdsNeedUpdate)
    }

    // Build final result
    const result = kols.map((kol) => ({
      kolId: kol.id,
      platform: kol.platform,
      platformId: kol.platformAccount || '',
      score: vectorSearchResult.find((i) => i.userId === kol.platformAccount)?.score ?? 0.0,
    }))

    return result
  }

  /**
   * 更新频道的videos数据
   * @param channelIds 需要更新的频道ID列表
   */
  private async updateMilvusChannelsWithVideos(channelIds: string[]): Promise<void> {
    try {
      console.log(
        `[updateMilvusChannelsWithVideos] Starting to update videos for ${channelIds.length} channels`,
      )

      await Bluebird.map(
        channelIds,
        async (channelId: string) => {
          try {
            const channelVideos = await YoutubeApi.getInstance().getChannelVideos(channelId)

            if (!channelVideos?.length) {
              console.warn(`Channel ${channelId} not found or no videos, skipping`)
              return
            }

            const recentVideos = channelVideos.slice(0, 10)
            let videosAverageViewCount = 0

            if (recentVideos.length > 0) {
              videosAverageViewCount =
                recentVideos.reduce((sum, video) => sum + (Number(video.viewCount) || 0), 0) /
                recentVideos.length
            }

            await prisma.youTubeChannel.update({
              where: { channelId },
              data: {
                videos: channelVideos as any,
                videosAverageViewCount: Math.round(videosAverageViewCount),
                updatedAt: new Date(),
              },
            })
          } catch (error) {
            console.error(`Failed to update videos for channel ${channelId}:`, error)
          }
        },
        { concurrency: 20 },
      )

      console.log(`[updateMilvusChannelsWithVideos] Completed updating videos for channels`)
    } catch (error) {
      console.error('[updateMilvusChannelsWithVideos] Failed to update channel videos:', error)
    }
  }

  public async youtubeProcessSimilarJobByVisualSimilarity(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>
    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
    const { ytbVideoIds, source, channelId } = job.data.params as SimilarTaskParams
    const collector = new RapidApiStatsCollector()

    const ytbChannelId =
      channelId || source || (await YoutubeApi.getInstance().getYoutubeChannelId(params.source))

    if (!ytbChannelId) {
      throw new Error('no source id found, task failed')
    }

    const project = await prisma.project.findFirstOrThrow({
      where: { id: task.projectId, deletedAt: null },
    })
    const projectCfg = (project.config ?? {}) as Partial<ProjectConfig>

    // rated,candidate,exclude channel Ids
    const [projectRatedChannelIds, previousCandidatesChannelIds, excludeChannelIds] =
      await metrics.withPhase('get_project_all_rated_channel_ids', () =>
        Promise.all([
          ProjectKolService.getProjectKolUniques(task.projectId, {
            platform: [KolPlatform.YOUTUBE],
          }),
          this.getPreviousTasksCandidatesChannelIds(task.projectId),
          getExcludeIds(task.createdBy!, KolPlatform.YOUTUBE),
        ]),
      )
    const allRatedChannelIds = [
      ...new Set([
        ...projectRatedChannelIds,
        ...previousCandidatesChannelIds,
        ...excludeChannelIds,
      ]),
    ]
    metrics.set('project_all_rated_channel_count', allRatedChannelIds.length)

    // source channel
    const sourceChannel = await metrics.withPhase('get_source_channel', () =>
      YoutubeApi.getInstance().getChannelWithVideos(ytbChannelId, collector),
    )
    if (!sourceChannel?.videos?.length) {
      throw new Error('no videos found for source channel')
    }

    // get 3 elements
    const kolDescription = params.kolDescription ?? projectCfg.kolDescription ?? ''
    const allowList = params.allowList ?? projectCfg.allowList ?? []
    const banList = params.banList ?? projectCfg.banList ?? []

    metrics.set('three_elements', {
      kolDescription,
      allowList,
      banList,
    })

    // get related channels
    let relatedChannels: Channel[] = []
    if (!ytbVideoIds?.length) {
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByRapidApi(sourceChannel, params.regions, collector),
      )
    } else {
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByVideoIds(ytbVideoIds, params.regions, collector),
      )
    }
    if (!relatedChannels?.length) {
      throw new Error('no related channels found')
    }
    metrics.set('source_channel_related_channels_count', relatedChannels.length)

    const filteredRelatedChannels = relatedChannels.filter(
      (channel) => channel.channelId && !allRatedChannelIds.includes(channel.channelId),
    )

    if (filteredRelatedChannels.length === 0) {
      throw new Error('no related channels found after rated filter')
    }
    metrics.set('after_rated_filter_related_channels_count', filteredRelatedChannels.length)

    if (!filteredRelatedChannels.some((c) => c.channelId === sourceChannel.channelId)) {
      filteredRelatedChannels.unshift(sourceChannel)
    }

    await metrics.withPhase(
      'upsert_kol_info_and_channel_vectors',
      async () =>
        await Promise.all([
          this.upsertKolInfo(task.createdBy ?? undefined, filteredRelatedChannels),
          this.processAndUpsertChannels(filteredRelatedChannels),
        ]),
    )

    const visualAnalysisInput = filteredRelatedChannels
      .filter((channel) => channel.videos && channel.videos.length > 0)
      .map((channel) => ({
        username: channel.channelHandle,
        posts: channel.videos.slice(0, 6),
      }))

    // visual similarity analysis
    const visualSimilarityResult = await metrics.withPhase(
      'analyze_visual_similarity',
      async () => {
        const batchSize = 10
        const batches = Array.from(
          { length: Math.ceil(visualAnalysisInput.length / batchSize) },
          (_, i) => visualAnalysisInput.slice(i * batchSize, (i + 1) * batchSize),
        )

        const analysisResults = await Bluebird.map(
          batches,
          async (batch, batchIndex) => {
            try {
              return await analyzeVisualSimilarityService.analyzeVisualSimilarity(batch, {
                kolDescription,
                allowList,
                banList,
              })
            } catch (error) {
              console.error(`process batch ${batchIndex + 1} error:`, error)
              return []
            }
          },
          { concurrency: 50 },
        )
        return analysisResults.flat()
      },
    )

    // filter valid results
    const validResults = visualSimilarityResult.filter(
      (result: any) => result.score === 100 && result.username !== sourceChannel.channelHandle,
    )

    if (validResults.length === 0) {
      return []
    }

    metrics.set('after_visual_similarity_filter_count', validResults.length)
    metrics.set('visual_results', visualSimilarityResult)

    const channelHandleToId = new Map(
      filteredRelatedChannels.map((c) => [c.channelHandle, c.channelId]),
    )
    const channelScores = validResults
      .map((result: any) => ({
        id: channelHandleToId.get(result.username) || '',
        score: result.score / 100,
      }))
      .filter((item: { id: string }) => item.id)

    const kols = await metrics.withPhase('db_query_kol_info', () =>
      prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.YOUTUBE,
          youtubeChannel: {
            channelId: {
              in: channelScores.map((i) => i.id),
            },
            numericSubscriberCount: {
              gte: params.minSubscribers,
              lte: params.maxSubscribers,
            },
            videosAverageViewCount: {
              gte: params.videosAverageViews,
              lte: params.maxVideosAverageViews,
            },
            country: params.regions?.length ? { in: params.regions } : undefined,
            lastPublishedTime: {
              gte: dayjs().subtract(+YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS, 'day').unix(),
            },
          },
        },
        include: { youtubeChannel: true },
      }),
    )

    metrics.set('after_db_filter_count', kols.length)
    metrics.set('final_kols_count', kols.length)
    metrics.set('rapid_api_stats', collector.getStats())

    await YoutubeService.getInstance().superlikeSourceChannel(params, id)

    return kols
      .map((kol: any) => ({
        kolId: kol.id,
        platform: kol.platform,
        platformId: kol.platformAccount || '',
        score: channelScores.find((i) => i.id === kol.platformAccount)?.score || 0.0,
      }))
      .sort((a, b) => b.score - a.score)
  }

  private async getRelatedChannelsByRapidApi(
    sourceChannel: Channel,
    regions: string[] | undefined,
    collector?: RapidApiStatsCollector,
  ): Promise<Channel[]> {
    const videos = sourceChannel.videos.slice(0, YOUTUBE_USER_VIDEO_COUNT)
    if (videos.length == 0) {
      throw new Error('[rapidapi]cannot get videos for source channel: ' + sourceChannel.channelId)
    }
    if (videos.length < YOUTUBE_USER_VIDEO_COUNT) {
      console.warn(
        `[rapidapi]get videos for source channel ${sourceChannel.channelId} is insufficient, got ${videos.length}`,
      )
    }

    // concurrent get related videos
    const allRelatedVideos = await Bluebird.map(
      videos.map((i) => i.videoId),
      async (videoId) => {
        try {
          const related = await YoutubeApi.getInstance().getVideoRelatedVideo(
            videoId,
            YOUTUBE_RELATED_VIDEO_COUNT,
            collector,
          )

          console.log(
            `[rapidapi]get ${related.length} videos by source channel ${sourceChannel.channelId}'s source video ${videoId}`,
          )
          return related.slice(0, YOUTUBE_RELATED_VIDEO_COUNT)
        } catch (err) {
          Sentry.captureException(err)
          console.log(`getRelatedChannelsByRapidApi failed: ${err}`)
          return []
        }
      },
      { concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS },
    )

    const channelIds = [...new Set(allRelatedVideos.flat().map((v) => v.channelId))]
    console.log(
      `[rapidapi]filter ${channelIds.length} channels from ${allRelatedVideos.flat().length} videos`,
    )
    const allRelatedChannels = await Bluebird.map(
      channelIds,
      async (relatedChannelId) => {
        try {
          if (relatedChannelId) {
            return await YoutubeApi.getInstance().getChannelWithVideos(relatedChannelId, collector)
          } else {
            return undefined
          }
        } catch (error) {
          Sentry.captureException(error)
          console.log(`getRelatedChannelsByRapidApi failed: ${error}`)
          return undefined
        }
      },
      {
        concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS,
      },
    )
    return allRelatedChannels.filter((c): c is Channel => !!c && !!c?.videos?.length)
  }

  private async getRelatedChannelsByVideoIds(
    videoIds: string[],
    regions: string[] | undefined,
    collector?: RapidApiStatsCollector,
  ): Promise<Channel[]> {
    if (videoIds.length === 0) {
      return []
    }

    const allRelatedVideos = await Bluebird.map(
      videoIds,
      async (videoId) => {
        try {
          const related = await YoutubeApi.getInstance().getVideoRelatedVideo(
            videoId,
            YOUTUBE_RELATED_VIDEO_COUNT,
            collector,
          )
          console.log(`[rapidapi]get related videos  ${related.length} videos by ${videoId}`)
          return related.slice(0, YOUTUBE_RELATED_VIDEO_COUNT)
        } catch (err) {
          Sentry.captureException(err)
          console.log(`getRelatedChannelsByRapidApi failed: ${err}`)
          return []
        }
      },
      { concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS },
    )

    const channelIds = [...new Set(allRelatedVideos.flat().map((v) => v.channelId))]
    console.log(
      `[rapidapi]filter ${channelIds.length} channels from ${allRelatedVideos.flat().length} videos`,
    )
    const allRelatedChannels = await Bluebird.map(
      channelIds,
      async (relatedChannelId) => {
        try {
          if (relatedChannelId) {
            return await YoutubeApi.getInstance().getChannelWithVideos(relatedChannelId, collector)
          } else {
            return undefined
          }
        } catch (error) {
          Sentry.captureException(error)
          console.log(`getRelatedChannelsByRapidApi failed: ${error}`)
          return undefined
        }
      },
      {
        concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS,
      },
    )
    return allRelatedChannels.filter((c): c is Channel => !!c && !!c?.videos?.length)
  }

  public async updateOfficialEmail(
    channel: YouTubeChannel,
  ): Promise<Omit<YouTubeChannel, 'videos'>> {
    const fullInfo = await YoutubeApi.getInstance().getChannel(channel.channelId)
    if (!fullInfo) {
      console.log('[rapidapi]channel not found: ' + channel.channelId)
    }
    return prisma.youTubeChannel.update({
      where: { channelId: channel.channelId },
      data: { officialEmail: fullInfo?.hasEmail ?? false },
    })
  }

  public async updatePublishTime(
    channel: YouTubeChannel,
  ): Promise<Omit<YouTubeChannel, 'videos'> | undefined> {
    const fullInfo = await YoutubeApi.getInstance().getChannelWithVideos(channel.channelId)
    if (!fullInfo) {
      console.log('[rapidapi]channel not found: ' + channel.channelId)
      return undefined
    }
    let publishTime = 0
    if (fullInfo.videos && fullInfo.videos.length > 0) {
      publishTime = getUnixTimestamp(parsePublishedTime(fullInfo.videos[0].publishedTimeText))
      if (publishTime) {
        return prisma.youTubeChannel.update({
          where: { channelId: channel.channelId },
          data: { lastPublishedTime: publishTime },
        })
      }
    }
    return undefined
  }

  private async processAndUpsertChannels(channels: Channel[]) {
    const processedChannels = channels
      .map((channel: Channel) => {
        try {
          // 计算发布统计
          const publicationStats = PublicationStatsService.calculatePublicationStats(
            channel.videos ?? [],
            KolPlatform.YOUTUBE,
          )
          return {
            channelId: channel.channelId,
            channelName: channel.title,
            channelHandle: channel.channelHandle ?? '',
            channelDescription: channel.description ?? '',
            numericSubscriberCount: BigInt(channel.subscriberCount ?? 0),
            country: channel.country ?? '',
            haveCrawlered: false,
            lastPublishedTime: BigInt(channel.lastUpdatedAt ?? 0),
            videosAverageViewCount: BigInt(channel.videosAverageViewCount ?? 0),
            officialEmail: !!channel.hasEmail,
            videos: (channel.videos ?? []) as Prisma.InputJsonValue,
            publicationStats: publicationStats as unknown as Prisma.InputJsonValue,
            createdAt: new Date(),
            updatedAt: new Date(),
          }
        } catch (err) {
          console.log(`failed to upsert youtube channel ${channel.channelId}: ${err}`)
          Sentry.captureException(err)
          return null
        }
      })
      .filter((i) => i) as YouTubeChannel[]

    // 获取所有已存在的channelId
    const existingChannels = await prisma.youTubeChannel.findMany({
      where: { channelId: { in: processedChannels.map((c) => c.channelId) } },
      select: { channelId: true },
    })
    const existingChannelIds = new Set(existingChannels.map((c) => c.channelId))

    // 分离需要更新和需要插入数据
    const channelsToUpdate = processedChannels.filter((c) => existingChannelIds.has(c.channelId))
    const channelsToInsert = processedChannels.filter((c) => !existingChannelIds.has(c.channelId))

    // 批量更新已存在的记录
    if (channelsToUpdate.length > 0) {
      await Bluebird.map(
        channelsToUpdate,
        async (channel) => {
          try {
            await prisma.youTubeChannel.update({
              where: { channelId: channel.channelId },
              data: {
                channelName: channel.channelName,
                channelHandle: channel.channelHandle,
                channelDescription: channel.channelDescription,
                numericSubscriberCount: channel.numericSubscriberCount,
                country: channel.country,
                lastPublishedTime: channel.lastPublishedTime,
                videosAverageViewCount: channel.videosAverageViewCount,
                officialEmail: channel.officialEmail,
                videos: channel.videos as Prisma.InputJsonValue,
                publicationStats: channel.publicationStats as unknown as Prisma.InputJsonValue,
                updatedAt: channel.updatedAt,
              },
            })
          } catch (err) {
            Sentry.captureException(err)
            console.log(`upsertChannels failed: ${err}`)
          }
        },
        { concurrency: 50 },
      )
      console.log(`update ${channelsToUpdate.length} channels`)
    }

    // 批量插入新记录
    if (channelsToInsert.length > 0) {
      const insertResult = await prisma.youTubeChannel.createMany({
        data: channelsToInsert.map((channel) => ({
          ...channel,
          videos: channel.videos as Prisma.InputJsonValue,
          publicationStats: channel.publicationStats as unknown as Prisma.InputJsonValue,
        })),
        skipDuplicates: true,
      })
      console.log(`insert ${insertResult.count} youtube channels`)
    }
  }

  private async upsertKolInfo(userId: string | undefined, channels: Channel[]) {
    // 通用的 ID 是 UC 开头的长 ID
    const channelIds = channels.map((i) => i.channelId)
    const exists = await prisma.kolInfo.findMany({
      where: {
        platformAccount: {
          in: channelIds,
        },
      },
      select: { platformAccount: true, email: true, historyEmails: true },
    })
    const existIds = exists.map((i) => i.platformAccount)
    const existMap = new Map(exists.map((i) => [i.platformAccount, i]))
    const updates = channels.filter((i) => existIds.includes(i.channelId))
    const inserts = channels.filter((i) => !existIds.includes(i.channelId))
    const insertKols = inserts.map((i) => {
      return {
        title: i.title,
        description: i.description,
        email: i.email,
        emailSource: i.emailSource,
        links: i.links
          ? i.links.map((i) => (i.link.startsWith('https://') ? i.link : `https://${i.link}`))
          : [],
        platformAccount: i.channelId,
        createdAt: new Date(),
        updatedAt: new Date(),
        avatar: i.avatar?.length ? i.avatar[0].url : '',
        platform: KolPlatform.YOUTUBE,
      }
    })
    await prisma.kolInfo.createMany({ data: insertKols, skipDuplicates: true })
    await Bluebird.map(
      updates,
      async (update) => {
        try {
          const exist = existMap.get(update.channelId)
          const emailFields: EmailFields = {
            email: undefined,
            emailSource: undefined,
            emailUpdatedAt: undefined,
            historyEmails: undefined,
          }
          const needUpdateEmail = update.email && update.email !== exist?.email
          if (needUpdateEmail) {
            emailFields.email = update.email
            emailFields.emailSource = update.emailSource as EmailSourceType
            emailFields.emailUpdatedAt = new Date()
            emailFields.historyEmails = [
              ...new Set([...(exist?.historyEmails ?? []), update.email as string]),
            ]
            // email audit log
            EmailService.getInstance().addEmailAuditLog(
              userId,
              update.channelId,
              update.email,
              update.emailSource as EmailSourceType,
            )
          }

          await prisma.kolInfo.update({
            where: {
              platform_platformAccount: {
                platformAccount: update.channelId,
                platform: KolPlatform.YOUTUBE,
              },
            },
            data: {
              title: update.title,
              description: update.description,
              platformAccount: update.channelId,
              platform: KolPlatform.YOUTUBE,
              avatar: update.avatar?.length ? update.avatar[0].url : '',
              links: update.links
                ? update.links.map((i) =>
                    i.link.startsWith('https://') ? i.link : `https://${i.link}`,
                  )
                : [],
              ...emailFields,
              historyEmails: emailFields.historyEmails ?? exist?.historyEmails,
            },
          })
        } catch (err) {
          Sentry.captureException(err)
          console.log(`upsertKolInfo failed: ${err}`)
        }
      },
      { concurrency: 50 },
    )
  }

  private async upsertChannelInfoAndVectors(channels: Channel[]) {
    console.log(
      `[upsertChannelInfoAndVectors] 开始处理 ${channels.length} 个YouTube频道数据并生成向量存储`,
    )
    try {
      // 创建频道数据映射，方便后续查询
      const channelMap = new Map<string, Channel>()
      channels.forEach((channel) => {
        channelMap.set(channel.channelId, channel)
      })

      // 并行处理频道数据和生成embedding映射
      const [processedChannels, embeddingMap] = await Promise.all([
        // 处理频道基础数据
        Promise.resolve().then(() => {
          const youtubeChannels = channels
            .map((channel: Channel) => {
              try {
                // Calculate publication stats for the channel
                const publicationStats = PublicationStatsService.calculatePublicationStats(
                  channel.videos,
                  KolPlatform.YOUTUBE,
                )

                return {
                  channelId: channel.channelId,
                  channelName: channel.title,
                  channelHandle: channel.channelHandle ?? '',
                  channelDescription: channel.description ?? '',
                  numericSubscriberCount: BigInt(channel.subscriberCount ?? 0),
                  country: channel.country ?? '',
                  haveCrawlered: false,
                  lastPublishedTime: BigInt(
                    channel.lastUpdatedAt || publicationStats.lastPublishedTime || 0,
                  ),
                  videosAverageViewCount: BigInt(channel.videosAverageViewCount ?? 0),
                  officialEmail: !!channel.hasEmail,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  videos: channel.videos as Prisma.InputJsonValue,
                  publicationStats: publicationStats as unknown as Prisma.InputJsonValue,
                }
              } catch (err) {
                console.log(`处理YouTube频道数据失败 ${channel.channelId}: ${err}`)
                Sentry.captureException(err)
                return null
              }
            })
            .filter((i) => i) as YouTubeChannel[]
          console.log(
            `[upsertChannelInfoAndVectors] 成功处理 ${youtubeChannels.length} 个频道基础数据`,
          )
          return youtubeChannels
        }),
        // 生成嵌入向量映射
        (async () => {
          try {
            // 使用现有的generateEmbeddingsForYoutube方法生成embedding
            const embeddings = await generateEmbeddingsForYoutube(channels)
            const embeddingMap = new Map<string, number[]>()

            channels.forEach((channel, index) => {
              if (embeddings[index] && embeddings[index].length > 0) {
                embeddingMap.set(channel.channelId, embeddings[index])
              }
            })

            console.log(
              `[upsertChannelInfoAndVectors] 成功生成 ${embeddingMap.size} 个嵌入向量映射`,
            )
            return embeddingMap
          } catch (error) {
            console.error(`[upsertChannelInfoAndVectors] 生成嵌入向量失败: ${error}`)
            Sentry.captureException(error)
            return new Map<string, number[]>()
          }
        })(),
      ])

      console.log(`[upsertChannelInfoAndVectors] 成功生成 ${embeddingMap.size} 个嵌入向量映射`)

      // 将处理好的数据与向量匹配
      const channelsWithEmbeddings = processedChannels
        .map((channel) => {
          const embedding = embeddingMap.get(channel.channelId)
          const originalChannel = channelMap.get(channel.channelId)

          if (embedding && embedding.length > 0 && originalChannel) {
            return {
              channel,
              originalChannel,
              embedding,
            }
          }
          if (!embedding || embedding.length === 0) {
            console.warn(
              `[upsertChannelInfoAndVectors] 频道 ${channel.channelId} 没有有效的向量数据`,
            )
          }
          if (!originalChannel) {
            console.warn(`[upsertChannelInfoAndVectors] 找不到频道 ${channel.channelId} 的原始数据`)
          }
          return null
        })
        .filter(
          (
            item,
          ): item is {
            channel: YouTubeChannel
            originalChannel: Channel
            embedding: number[]
          } => item !== null,
        )

      console.log(
        `[upsertChannelInfoAndVectors] 成功匹配 ${channelsWithEmbeddings.length} 个有效频道数据和向量`,
      )

      // 批量处理，每次处理200个频道
      const batchSize = 200
      const batches = []
      for (let i = 0; i < channelsWithEmbeddings.length; i += batchSize) {
        batches.push(channelsWithEmbeddings.slice(i, i + batchSize))
      }

      // 处理YouTube频道数据
      for (const batch of batches) {
        const channelIds = batch.map((item) => item.channel.channelId)
        const existingChannels = await prisma.youTubeChannel.findMany({
          where: { channelId: { in: channelIds } },
          select: { channelId: true },
        })
        const existingChannelIds = new Set(existingChannels.map((c) => c.channelId))

        const channelsToUpdate = batch.filter((item) =>
          existingChannelIds.has(item.channel.channelId),
        )
        const channelsToInsert = batch.filter(
          (item) => !existingChannelIds.has(item.channel.channelId),
        )

        if (channelsToUpdate.length > 0) {
          await Bluebird.map(
            channelsToUpdate,
            async (item) => {
              try {
                await prisma.youTubeChannel.update({
                  where: { channelId: item.channel.channelId },
                  data: {
                    channelName: item.channel.channelName,
                    channelHandle: item.channel.channelHandle,
                    channelDescription: item.channel.channelDescription,
                    numericSubscriberCount: item.channel.numericSubscriberCount,
                    country: item.channel.country,
                    lastPublishedTime: item.channel.lastPublishedTime,
                    videosAverageViewCount: item.channel.videosAverageViewCount,
                    officialEmail: item.channel.officialEmail,
                    updatedAt: item.channel.updatedAt,
                    videos: item.channel.videos as Prisma.InputJsonValue,
                    publicationStats: item.channel.publicationStats as Prisma.InputJsonValue,
                  },
                })
              } catch (err) {
                Sentry.captureException(err)
                console.error(`更新频道 ${item.channel.channelId} 失败:`, err)
              }
            },
            { concurrency: 50 },
          )
          console.log(`[upsertChannelInfoAndVectors] 已更新 ${channelsToUpdate.length} 个频道数据`)
        }
        if (channelsToInsert.length > 0) {
          try {
            const insertResult = await prisma.youTubeChannel.createMany({
              data: channelsToInsert.map((item) => item.channel) as any,
              skipDuplicates: true,
            })
            console.log(`[upsertChannelInfoAndVectors] 已插入 ${insertResult.count} 个新频道数据`)
          } catch (error) {
            console.error(`[upsertChannelInfoAndVectors] 批量插入频道数据失败:`, error)
            Sentry.captureException(error)
          }
        }
      }

      // 准备Milvus数据
      const milvusDataToInsert = channelsWithEmbeddings
        .map((item) => {
          try {
            const { originalChannel, embedding, channel } = item
            // 构建EnhancedMilvusData对象
            const data = {
              userId: channel.channelId,
              account: channel.channelHandle || '',
              nickname: channel.channelName || '',
              platform: KolPlatform.YOUTUBE,
              region: channel.country || '',
              followerCount: Number(channel.numericSubscriberCount) || 0,
              averagePlayCount: Number(channel.videosAverageViewCount) || 0,
              lastPublishedTime: Number(channel.lastPublishedTime) || 0,
              videoTexts: originalChannel.videos
                .slice(0, 10)
                .map((v) => (v.title || '').slice(0, 500))
                .join(' ')
                .slice(0, 6000),
              email: originalChannel.email || '',
              denseVector: embedding,
              sparseVector: { indices: [], values: [] },
              createdAt: Math.floor(Date.now() / 1000),
              updatedAt: Math.floor(Date.now() / 1000),
            }
            return data as EnhancedMilvusData
          } catch (mapError) {
            console.error(`[upsertChannelInfoAndVectors] 处理频道数据时出错:`, mapError)
            return null
          }
        })
        .filter((data): data is EnhancedMilvusData => data !== null)

      // 插入Milvus数据
      try {
        if (milvusDataToInsert.length === 0) {
          console.warn('[upsertChannelInfoAndVectors] 没有有效的向量数据可插入Milvus')
        } else {
          console.log(
            `[upsertChannelInfoAndVectors] 准备插入 ${milvusDataToInsert.length} 条记录到Milvus`,
          )
          // 记录第一条数据的结构以便调试
          if (milvusDataToInsert.length > 0) {
            const firstItem = milvusDataToInsert[0]
            console.log('[upsertChannelInfoAndVectors] 第一条数据的字段类型:')
            Object.entries(firstItem).forEach(([key, value]) => {
              console.log(`${key}: ${typeof value} ${Array.isArray(value) ? '(Array)' : ''}`)
              if (key === 'denseVector') {
                console.log(`denseVector length: ${value.length}`)
              }
              if (key === 'sparseVector') {
                console.log(`sparseVector structure:`, JSON.stringify(value))
              }
            })
          }

          // 使用bulkInsertEnhancedWithDelete方法进行插入
          const milvusResult = await bulkInsertEnhancedWithDelete(milvusDataToInsert)
          console.log(
            `[upsertChannelInfoAndVectors] Milvus数据操作完成:\n` +
              `- 删除记录数: ${milvusResult.deleteResult.delete_cnt}\n` +
              `- 插入成功: ${milvusResult.insertResult.succ_index.length}\n` +
              `- 插入失败: ${milvusResult.insertResult.err_index?.length || 0}`,
          )
        }
      } catch (milvusError) {
        console.error('[upsertChannelInfoAndVectors] Milvus数据插入失败:', milvusError)
        if (milvusError instanceof Error) {
          console.error(
            `[upsertChannelInfoAndVectors] 错误详情: ${milvusError.message}, 错误堆栈: ${milvusError.stack}`,
          )
        }
        Sentry.captureException(milvusError)
      }

      return channels.map((channel) => channel.channelId)
    } catch (error) {
      console.error('[upsertChannelInfoAndVectors] 整体处理失败:', error)
      Sentry.captureException(error)
      throw error
    }
  }

  async createKolByRapidApi(idOrName: string): Promise<KolInfo> {
    let id: string | undefined = idOrName
    if (!idOrName.startsWith('UC')) {
      id = await YoutubeApi.getInstance().getYoutubeChannelId(idOrName)
    }
    if (!id) {
      throw new Error(`cannot find kol info for youtube: ${idOrName}`)
    }
    const channel = await YoutubeApi.getInstance().getChannel(id)
    const updates = {
      title: channel.title,
      description: channel.description,
      email: channel.email,
      emailSource: channel.emailSource,
      historyEmails: [],
      emailUpdatedAt: new Date(),
    }
    const result = await prisma.kolInfo.upsert({
      where: {
        platform_platformAccount: { platform: KolPlatform.YOUTUBE, platformAccount: id },
      },
      update: updates,
      create: updates && {
        platform: KolPlatform.YOUTUBE,
        platformAccount: channel.channelId,
      },
    })
    return result
  }

  private async getChannelsFromHashtag(
    hashtag: string,
    initialPaginationToken: string,
    maxVideoCount: number,
    currentVideoCount: number,
    options: {
      ratedChannelIds: string[]
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakResult> {
    const channelIds = new Set<string>()

    let currentPaginationToken = initialPaginationToken
    let hasMore = true
    let message = ''
    const ratedChannelIdsSet: Set<string> = options.ratedChannelIds
      ? new Set<string>(options.ratedChannelIds)
      : new Set<string>()
    let videoCount = 0
    let total = 0

    let maxTime = maxVideoCount / 25 // 假设 25 个视频是平均获取数量的下限
    while (videoCount < maxVideoCount && hasMore && maxTime > 0) {
      try {
        const hashtagOptions = {
          token: currentPaginationToken || undefined,
          geo: HASHTAG_DEFAULT_GEO,
          lang: HASHTAG_DEFAULT_LANG,
        }
        const response = await YoutubeApi.getInstance().getHashtag(
          hashtag,
          hashtagOptions,
          collector,
        )
        if (response.meta.videoCount > total) {
          total = response.meta.videoCount
        }

        if (!response || !response.data) {
          hasMore = false
          message = '没有更多内容,请求接口失败'
          break
        }

        const videos = response.data || []
        videos.map((video) => {
          if (video.channelId) {
            channelIds.add(video.channelId)
          }
        })

        videoCount += videos.length
        console.log(`标签 ${hashtag} 当前获取到 ${videos.length} 个视频`)
        if (!response.continuation || response.data.length === 0) {
          hasMore = false
          message = '没有更多内容,分页token为空或结果列表为空'
          break
        }
        currentPaginationToken = response.continuation
        maxTime--
      } catch (error) {
        console.error(`获取标签 ${hashtag} 的内容时出错: ${error}`)
        hasMore = false
        message = `获取标签 ${hashtag} 的内容时出错: ${error}`
        break
      }
    }

    const uniqueChannelIds = logFilter(
      Array.from(channelIds),
      (id) => !ratedChannelIdsSet.has(id),
      'filter rated channel ids',
    )

    return {
      channelIds: uniqueChannelIds,
      hasMore,
      paginationToken: currentPaginationToken,
      total: uniqueChannelIds.length,
      message,
      progress: {
        total: total,
        current: uniqueChannelIds.length,
        count: videoCount + currentVideoCount,
      },
    }
  }

  private async getChannelsFromSearch(
    query: string,
    initialPaginationToken: string,
    maxVideoCount: number,
    currentVideoCount: number,
    total: number,
    options: {
      ratedChannelIds: string[]
    },
    queryParams: {
      geo?: string
      lang?: string
      duration?: string
      sort_by?: string
      upload_date?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakResult> {
    const channelIds = new Set<string>()

    let hasMore = true
    let message = ''
    const ratedChannelIdsSet: Set<string> = options.ratedChannelIds
      ? new Set<string>(options.ratedChannelIds)
      : new Set<string>()
    let videoCount = 0

    const searchParams = {
      ...queryParams,
      token: initialPaginationToken,
    }

    // 先搜索
    let maxTime = maxVideoCount / 18 // 假设 18 个视频是平均获取数量的下限
    while (videoCount < maxVideoCount && hasMore && maxTime > 0) {
      try {
        const response = await YoutubeApi.getInstance().searchVideo(query, searchParams, collector)
        if (!response || !response.data) {
          hasMore = false
          message = '没有更多内容,请求接口失败or数据为空'
          break
        }

        const videos = response.data || []
        videos.map((video) => {
          if (video.channelId) {
            channelIds.add(video.channelId)
          }
        })

        videoCount += videos.length
        console.log(`搜索词 ${query} 当前获取到 ${videos.length} 个视频`)
        if (!response.token || response.data.length === 0) {
          if (searchParams.duration === 'long') {
            console.log('long duration, change to medium')
            searchParams.duration = 'medium'
            searchParams.token = ''
            maxTime = maxVideoCount / 18
            continue
          }
          hasMore = false
          message = '没有更多内容,分页token为空'
          break
        }
        searchParams.token = response.token
        maxTime--
      } catch (error) {
        console.error(`获取搜索词 ${query} 的内容时出错: ${error}`)
        hasMore = false
        message = `获取搜索词 ${query} 的内容时出错: ${error}`
        break
      }
    }

    // 再过滤
    const uniqueChannelIds = logFilter(Array.from(channelIds), (id) => !ratedChannelIdsSet.has(id))
    console.log(`[getUniqueChannelFromHashtag] 去重后获取到 ${uniqueChannelIds.length} 个用户ID`)

    return {
      channelIds: uniqueChannelIds,
      hasMore,
      paginationToken: searchParams.token,
      total: uniqueChannelIds.length,
      message,
      progress: {
        total: total,
        current: videoCount + currentVideoCount,
        count: uniqueChannelIds.length,
      },
    }
  }

  public async validateTag(
    tag: string,
    options: { geo?: string; lang?: string },
  ): Promise<boolean> {
    const hashtag = await YoutubeApi.getInstance().getHashtag(tag, options)
    return !!hashtag?.meta?.videoCount
  }

  /**
   * 获取历史任务的candidatesUsername
   */
  private async getPreviousTasksCandidatesChannelIds(projectId: string): Promise<string[]> {
    const previousTasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
    if (!previousTasks.length) {
      return []
    }
    const allResults = previousTasks
      .map((i) => i.result as unknown as YoutubeTaskResult)
      .filter((i) => i)
    const allCandidates = previousTasks.map((i) => {
      return i.candidate as unknown as Array<{
        kolId: string
        platform: KolPlatform
        platformId: string
        score: number
      }>
    })
    const allResultUsernames = allResults
      .flatMap((result) => result.candidates || [])
      .filter((candidate: any) => candidate.username)
      .map((candidate: any) => candidate.username as string)
    const allCandidatesUsernames = allCandidates
      .filter(
        (
          c,
        ): c is Array<{
          kolId: string
          platform: KolPlatform
          platformId: string
          score: number
        }> => Array.isArray(c),
      )
      .flatMap((c) => c.map((i) => i.platformId))
    return [...allCandidatesUsernames, ...allResultUsernames]
  }

  /**
   * 处理YouTube长时间爬取任务
   * @param job 任务数据
   * @returns 处理结果
   */
  public async processLongCrawlerJob(job: any) {
    const { id, params } = job.data

    try {
      console.info(`[processLongCrawlerJob] 开始处理YouTube爬取任务 ${id}`)

      // 获取任务信息和进度
      const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })

      const { projectId } = params
      let maxQuotaCost = params.maxQuotaCost

      const progress = task.meta as unknown as YtbLongCrawlerProgress

      if (!progress.pendingUsersQueue) {
        throw new Error('initialize pending users queue failed')
      }

      const priorityQueue = PriorityQueueFactory.fromJSON(
        progress.pendingUsersQueue,
        KolPlatform.YOUTUBE,
      ) as YoutubePriorityQueue

      if (priorityQueue.isEmpty()) {
        throw new Error('no pending users, task completed')
      }

      if (!progress.processedUsers) progress.processedUsers = []
      if (!progress.allPerfectFitUserNames) progress.allPerfectFitUserNames = []
      if (!progress.allContentOkUserNames) progress.allContentOkUserNames = []
      if (!progress.lastProcessedAt) progress.lastProcessedAt = new Date().toISOString()
      if (!progress.currentBatch) progress.currentBatch = 0
      if (!progress.batchLogs) progress.batchLogs = []
      if (!progress.allProcessedUsers) progress.allProcessedUsers = []
      if (!progress.hasConsumedQuotaCount) progress.hasConsumedQuotaCount = 0

      // get all users that should be excluded from processing
      const [projectKolRateChannelIds, candidateChannelIds, excludeChannelIds] = await Promise.all([
        ProjectKolService.getProjectKolUniques(projectId, {
          platform: [KolPlatform.YOUTUBE],
        }),
        this.getPreviousTasksCandidatesChannelIds(projectId),
        getExcludeIds(task.createdBy!, KolPlatform.YOUTUBE),
      ])

      console.info(
        `[processLongCrawlerJob] 项目 ${projectId} 去重信息: 已评价频道${projectKolRateChannelIds.length}个, 候选频道${candidateChannelIds.length}个, 排除频道${excludeChannelIds.length}个`,
      )

      // init processed users with all exclusion sources
      let processedSet = new Set([
        ...progress.allProcessedUsers,
        ...progress.processedUsers,
        ...projectKolRateChannelIds,
        ...candidateChannelIds,
        ...excludeChannelIds,
      ])

      // pending users x users per batch
      const batchSize = +LONG_CRAWLER_YTB_BATCH_SIZE // YouTube批次大小较小，因为需要获取视频数据
      let singleIterationQuotaCost = batchSize * 10

      // loop until priority queue is empty
      while (!priorityQueue.isEmpty()) {
        const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
        // loop check quota,user can through api to update quota and updated kolDescription
        const updatedParams = task.params as unknown as YtbLongCrawlerTaskParams
        const updatedMaxQuotaCost = updatedParams.maxQuotaCost
        const filters = updatedParams.filters

        if (updatedMaxQuotaCost !== maxQuotaCost) {
          maxQuotaCost = updatedMaxQuotaCost // update maxQuotaCost
          console.info(
            `[processLongCrawlerJob] 配额已更新: ${maxQuotaCost} -> ${updatedMaxQuotaCost}`,
          )
        }

        // get batch users from priority queue
        const currentBatchSize = Math.floor(
          Math.min(
            batchSize,
            priorityQueue.size(),
            (maxQuotaCost - progress.hasConsumedQuotaCount) / 10,
          ),
        )

        if (currentBatchSize === 0) {
          console.info(
            `[processLongCrawlerJob] 当前批次大小为零，无法继续处理。已消耗: ${progress.hasConsumedQuotaCount}，最大配额: ${maxQuotaCost}`,
          )
          break
        }

        // 从优先级队列获取用户（按优先级排序）
        const currentBatchUsers = priorityQueue.dequeueBatch(currentBatchSize)
        const currentBatch = currentBatchUsers.map((user) => user.username)

        // 记录批次元数据（用于日志和调试）
        const batchMetadata = currentBatchUsers.map((user) => ({
          username: user.username,
          attitude: user.attitude,
          priority: user.priority,
          source: user.source,
          videoIds: user.videoIds,
        }))

        console.info(
          `[processLongCrawlerJob] 当前批次用户优先级分布: ${JSON.stringify(
            batchMetadata.reduce(
              (acc, user) => {
                acc[user.attitude] = (acc[user.attitude] || 0) + 1
                return acc
              },
              {} as Record<string, number>,
            ),
          )}`,
        )

        singleIterationQuotaCost = currentBatchSize * 10
        if (maxQuotaCost - progress.hasConsumedQuotaCount < singleIterationQuotaCost) {
          console.info(
            `[processLongCrawlerJob] 配额不足以继续处理，已消耗: ${progress.hasConsumedQuotaCount}，最大配额: ${updatedMaxQuotaCost}`,
          )
          break
        }

        // update progress
        progress.currentBatch += 1
        progress.lastProcessedAt = new Date().toISOString()
        // record processed users
        currentBatch.forEach((username: string) => processedSet.add(username))
        progress.processedUsers.push(...currentBatch)
        console.info(
          `[processLongCrawlerJob] 开始处理第 ${progress.currentBatch} 批，共 ${currentBatch.length} 个用户`,
        )

        // 获取当前批次用户的videoIds
        const allVideoIds = currentBatchUsers.flatMap((user) => user.videoIds)
        const relatedChannels = await this.getRelatedChannelsByVideoIds(allVideoIds, undefined)

        if (relatedChannels.length === 0) {
          console.info(
            `[processLongCrawlerJob] 从当前批次的${currentBatch.length}个用户中获取到0个相关频道，跳过处理`,
          )

          // empty related channels batch logs
          const batchLog: YtbLongCrawlerBatchLogs = {
            batchNumber: progress.currentBatch,
            processedUsers: currentBatch,
            relatedChannelIds: [],
            uniqueRelatedChannelIds: [],
            perfectFitChannelIds: [],
            contentOkChannelIds: [],
            timestamp: new Date().toISOString(),
            quotaCost: 0,
            numberOfRuns: 0,
            message: 'related channels is empty,skip this batch handle',
            success: false,
          }

          if (!progress.batchLogs) progress.batchLogs = []
          progress.batchLogs.push(batchLog)

          console.info(
            `[processLongCrawlerJob] 批次#${batchLog.batchNumber}: 处理${currentBatch.length}个用户，获取0个相关频道，无法继续处理`,
          )

          // update all processed users,in order to rm duplicates
          progress.allProcessedUsers = Array.from(processedSet)
          await prisma.similarChannelTask.update({
            where: { id: task.id },
            data: { meta: { ...progress } },
          })

          continue
        }

        // get related channel ids
        const relatedChannelIds = relatedChannels.map((channel) => channel.channelId)

        // filter processed channel ids
        const newRelatedChannelIds = relatedChannelIds.filter(
          (channelId) => !processedSet.has(channelId),
        )
        const newRelatedChannels = relatedChannels.filter((channel) =>
          newRelatedChannelIds.includes(channel.channelId),
        )

        // check if newRelatedChannels is empty
        if (newRelatedChannels.length === 0) {
          console.info(
            `[processLongCrawlerJob] 从当前批次的${currentBatch.length}个用户中获取到${relatedChannels.length}个相关频道，但全部都已处理过，跳过处理`,
          )

          // empty new related channels batch logs
          const batchLog: YtbLongCrawlerBatchLogs = {
            batchNumber: progress.currentBatch,
            processedUsers: currentBatch,
            relatedChannelIds: relatedChannelIds,
            uniqueRelatedChannelIds: [],
            perfectFitChannelIds: [],
            contentOkChannelIds: [],
            timestamp: new Date().toISOString(),
            quotaCost: 0,
            numberOfRuns: 0,
            message: 'all related channels have been processed before, skip this batch handle',
            success: false,
          }

          if (!progress.batchLogs) progress.batchLogs = []
          progress.batchLogs.push(batchLog)

          console.info(
            `[processLongCrawlerJob] 批次#${batchLog.batchNumber}: 处理${currentBatch.length}个用户，获取${relatedChannels.length}个相关频道，但全部都已处理过，无法继续处理`,
          )

          // update all processed users,in order to rm duplicates
          progress.allProcessedUsers = Array.from(processedSet)
          await prisma.similarChannelTask.update({
            where: { id: task.id },
            data: { meta: { ...progress } },
          })

          continue
        }

        // add new related channel ids to processed set
        processedSet = new Set([...processedSet, ...newRelatedChannelIds])

        console.log(`the filter condition is ${JSON.stringify(filters)}`)

        // get content-ok and perfect-fit channels
        const { contentOkChannels, perfectFitChannels } =
          await this.processLongCrawlerBatchChannels(
            newRelatedChannels,
            filters,
            task.createdBy!,
            projectId,
            id,
          )

        // handler perfect fit channels
        if (perfectFitChannels.length > 0) {
          // update perfect fit channels list
          const perfectFitChannelIds = perfectFitChannels.map((channel) => channel.username)
          progress.allPerfectFitUserNames.push(...perfectFitChannelIds)

          // add perfect-fit channels to priority queue
          perfectFitChannels.forEach((channel) => {
            priorityQueue.enqueue(channel)
          })

          console.info(
            `[processLongCrawlerJob] 添加 ${perfectFitChannels.length} 个 Perfect-Fit 频道到优先级队列`,
          )
        }

        // handler content ok channels
        if (contentOkChannels.length > 0) {
          // update content ok channels list
          const contentOkChannelIds = contentOkChannels.map((channel) => channel.username)
          progress.allContentOkUserNames.push(...contentOkChannelIds)

          // add content-ok channels to priority queue
          contentOkChannels.forEach((channel) => {
            priorityQueue.enqueue(channel)
          })

          console.info(
            `[processLongCrawlerJob] 添加 ${contentOkChannels.length} 个 Content-OK 频道到优先级队列`,
          )
        }

        // batch logs
        const batchLog: YtbLongCrawlerBatchLogs = {
          batchNumber: progress.currentBatch,
          processedUsers: currentBatch,
          relatedChannelIds: relatedChannelIds,
          uniqueRelatedChannelIds: newRelatedChannelIds,
          perfectFitChannelIds: perfectFitChannels.map((c) => c.username),
          contentOkChannelIds: contentOkChannels.map((c) => c.username),
          timestamp: new Date().toISOString(),
          quotaCost: 0, // consume quota cost, default 0
          numberOfRuns: 0, // consume quota runs, default 0
          message: '', // message
          success: true,
        }

        // deduct quota
        let quotaDeductionSuccessful = true
        try {
          const deductDynamicQuotaParams: DeductDynamicQuotaParams = {
            userId: task.createdBy!,
            quotaType: QuotaType.LONG_CRAWLER,
            quota: singleIterationQuotaCost,
            projectId: projectId,
            taskId: task.id,
            metadata: {
              ...batchLog,
            },
          }
          // deduct quota
          await MembershipService.getInstance().deductDynamicQuota(deductDynamicQuotaParams)
          console.info(
            `[processLongCrawlerJob] 成功扣除 ${singleIterationQuotaCost} 配额，累计已消耗 ${progress.hasConsumedQuotaCount} 配额`,
          )

          batchLog.quotaCost = singleIterationQuotaCost
          batchLog.numberOfRuns = batchSize
          batchLog.message = 'success'
          batchLog.success = true
          progress.hasConsumedQuotaCount += singleIterationQuotaCost
        } catch (error: unknown) {
          console.error(`[processLongCrawlerJob] 扣除配额失败:`, error)
          // deduct quota failed
          batchLog.quotaCost = 0
          batchLog.numberOfRuns = 0
          quotaDeductionSuccessful = false // flag false
          progress.hasConsumedQuotaCount += 0
          batchLog.message = `deduct quota failed,pause task: ${error instanceof Error ? error.message : 'unknown error'}`
          batchLog.success = false
        }

        progress.batchLogs.push(batchLog)

        console.info(
          `[processLongCrawlerJob] 批次#${batchLog.batchNumber}: 处理${currentBatch.length}个用户，获取${batchLog.relatedChannelIds.length}个相关频道(新${batchLog.uniqueRelatedChannelIds.length}个)，Content-OK频道${contentOkChannels.length}个，Perfect-Fit频道${perfectFitChannels.length}个，配额消耗${batchLog.quotaCost}`,
        )

        // 构建新的候选数据结构 - 直接追加当前批次的频道数据
        const existingCandidates = (task.candidate as any) || {}
        const candidatesData = {
          timestamp: new Date().toISOString(),
          contentOkUsers: [...(existingCandidates.contentOkUsers || []), ...contentOkChannels],
          perfectFitUsers: [...(existingCandidates.perfectFitUsers || []), ...perfectFitChannels],
        }
        // update all processed users,in order to rm duplicates
        progress.allProcessedUsers = Array.from(processedSet)
        // 更新进度，包含优先级队列状态
        const updatedProgress = {
          ...progress,
          pendingUsersQueue: priorityQueue.toJSON(), // 序列化优先级队列
        }

        const updatedTask = await prisma.similarChannelTask.update({
          where: { id: task.id },
          data: {
            // if quota deduction failed, update task status to PAUSING
            ...(quotaDeductionSuccessful ? {} : { status: SimilarChannelTaskStatus.PAUSING }),
            meta: updatedProgress,
            candidate: candidatesData,
          },
        })

        console.info(
          `[processLongCrawlerJob] 第 ${progress.currentBatch} 批处理完成，已更新数据库进度信息`,
        )
        // check if task is not processing
        if (updatedTask.status !== SimilarChannelTaskStatus.PROCESSING) {
          console.info(`[processLongCrawlerJob] 任务 ${id} 的状态为${updatedTask.status}, 退出循环`)
          break
        }
      }

      // end update
      progress.allProcessedUsers = Array.from(processedSet)

      // 最终更新，包含优先级队列状态
      const finalProgress = {
        ...progress,
        pendingUsersQueue: priorityQueue.toJSON(),
      }

      const endUpdateTask = await prisma.similarChannelTask.update({
        where: { id: task.id },
        data: { meta: finalProgress },
      })

      const finalParams = endUpdateTask.params as unknown as YtbLongCrawlerTaskParams
      const finalMaxQuotaCost = finalParams.maxQuotaCost

      // update message
      const queueSize = priorityQueue.size()
      const queueStats = priorityQueue.getStats()

      let endReason = ''
      if (finalMaxQuotaCost - progress.hasConsumedQuotaCount <= singleIterationQuotaCost) {
        endReason = `配额${finalMaxQuotaCost}不足或耗尽，任务完成`
      } else if (queueSize === 0) {
        endReason = '任务完成，优先级队列为空，无法获取更多频道'
      } else if (endUpdateTask.status === SimilarChannelTaskStatus.PAUSING) {
        endReason = `用户主动暂停任务,任务状态${endUpdateTask.status},已处理 ${progress.processedUsers.length} 个用户，Perfect-Fit ${progress.allPerfectFitUserNames.length} 个，Content-OK ${progress.allContentOkUserNames.length} 个，待处理队列还有 ${queueSize} 个`
      } else if (endUpdateTask.status === SimilarChannelTaskStatus.COMPLETING) {
        endReason = `任务主动结束任务，任务状态${endUpdateTask.status},已处理 ${progress.processedUsers.length} 个用户，Perfect-Fit ${progress.allPerfectFitUserNames.length} 个，Content-OK ${progress.allContentOkUserNames.length} 个，待处理队列还有 ${queueSize} 个`
      } else {
        endReason = `任务状态${endUpdateTask.status},已处理 ${progress.processedUsers.length} 个用户，Perfect-Fit ${progress.allPerfectFitUserNames.length} 个，Content-OK ${progress.allContentOkUserNames.length} 个，待处理队列还有 ${queueSize} 个`
      }

      console.info(`[processLongCrawlerJob] 队列统计: ${JSON.stringify(queueStats)}`)

      // return result
      const result = {
        endReason,
      }

      console.info(`[processLongCrawlerJob] ${result.endReason}`)
      return result
    } catch (error) {
      console.error(`[processLongCrawlerJob] 处理YouTube爬取任务 ${id} 失败:`, error)
      throw error
    }
  }

  /**
   * 处理一批频道，进行筛选并返回匹配的频道
   * @param relatedChannels 相关频道列表
   * @param filters 筛选条件
   * @param createdBy 创建者ID
   * @param projectId 项目ID
   * @param taskId 任务ID
   * @returns 包含contentOkChannels和perfectFitChannels两个数组
   */
  private async processLongCrawlerBatchChannels(
    relatedChannels: Channel[],
    filters: YtbLongCrawlerFilters,
    createdBy: string,
    projectId: string,
    taskId: string,
  ): Promise<{
    contentOkChannels: YoutubePriorityUser[]
    perfectFitChannels: YoutubePriorityUser[]
  }> {
    try {
      const validChannelsWithVideos = relatedChannels.filter(
        (channel) => channel.videos && channel.videos.length > 0,
      )

      if (validChannelsWithVideos.length === 0) {
        console.info(
          `[processLongCrawlerBatchChannels] 所有 ${relatedChannels.length} 个频道都没有视频数据`,
        )
        return {
          contentOkChannels: [],
          perfectFitChannels: [],
        }
      }

      const aiFilterResults = await this.applyAiFilterForChannels(validChannelsWithVideos, filters)

      if (aiFilterResults.length === 0) {
        console.info(
          `[processLongCrawlerBatchChannels] 所有 ${validChannelsWithVideos.length} 个频道都未通过AI筛选`,
        )
        return {
          contentOkChannels: [],
          perfectFitChannels: [],
        }
      }

      // 根据AI筛选结果找到对应的原始频道数据
      const aiFilteredChannelIds = new Set(aiFilterResults.map((result) => result.username))
      const channelsAfterAiFilter = validChannelsWithVideos.filter((channel) =>
        aiFilteredChannelIds.has(channel.channelId),
      )

      // apply demographic filters (Perfect 频道)
      const perfectChannels = this.applyDemographicFiltersForChannels(
        channelsAfterAiFilter,
        filters,
      )

      // 创建 perfectFitChannels
      const perfectFitChannels: YoutubePriorityUser[] = perfectChannels.map((channel) => {
        const aiResult = aiFilterResults.find((result) => result.username === channel.channelId)

        // 优先使用AI分析返回的videoIds，如果没有则使用频道的前6个视频ID
        let videoIds: string[] = []
        if (aiResult?.videoIds && aiResult.videoIds.length > 0) {
          videoIds = aiResult.videoIds
          console.info(
            `[processLongCrawlerBatchChannels] Perfect频道 ${channel.channelId} 使用AI筛选的 ${videoIds.length} 个视频`,
          )
        } else {
          videoIds =
            channel.videos
              ?.slice(0, 6)
              .map((video) => video.videoId)
              .filter(Boolean) || []
          console.info(
            `[processLongCrawlerBatchChannels] Perfect频道 ${channel.channelId} 使用默认的 ${videoIds.length} 个视频`,
          )
        }

        return {
          username: channel.channelId,
          attitude: ProjectKolAttitude.LIKE,
          priority: calculateUserPriority(ProjectKolAttitude.LIKE, 'ai_discovered', 'perfect'),
          addedAt: Date.now(),
          source: 'ai_discovered',
          reason: aiResult?.reason || '',
          userType: 'perfect',
          videoIds,
        }
      })

      // 创建 contentOkChannels 数组（通过AI筛选但不是Perfect的频道）
      const perfectChannelIds = new Set(perfectChannels.map((channel) => channel.channelId))
      const contentOkChannels: YoutubePriorityUser[] = aiFilterResults
        .filter((aiResult) => !perfectChannelIds.has(aiResult.username))
        .map((aiResult) => {
          const channel = validChannelsWithVideos.find((ch) => ch.channelId === aiResult.username)
          if (!channel) {
            console.warn(
              `[processLongCrawlerBatchChannels] 无法找到channelId为 ${aiResult.username} 的频道，跳过该结果`,
            )
            return null
          }
          let videoIds: string[] = []
          if (aiResult.videoIds && aiResult.videoIds.length > 0) {
            videoIds = aiResult.videoIds
            console.info(
              `[processLongCrawlerBatchChannels] Content-OK频道 ${aiResult.username} 使用AI筛选的 ${videoIds.length} 个视频`,
            )
          } else {
            videoIds =
              channel?.videos
                ?.slice(0, 6)
                .map((video: any) => video.videoId)
                .filter(Boolean) || []
            console.info(
              `[processLongCrawlerBatchChannels] Content-OK频道 ${aiResult.username} 使用默认的 ${videoIds.length} 个视频`,
            )
          }

          return {
            username: aiResult.username,
            attitude: ProjectKolAttitude.LIKE,
            priority: calculateUserPriority(ProjectKolAttitude.LIKE, 'ai_discovered', 'content-ok'),
            addedAt: Date.now(),
            source: 'ai_discovered',
            reason: aiResult.reason || '',
            userType: 'content-ok',
            videoIds,
          }
        })
        .filter((result) => result !== null) as YoutubePriorityUser[]

      console.info(
        `[processLongCrawlerBatchChannels] 处理了 ${relatedChannels.length} 个频道，其中 ${validChannelsWithVideos.length} 个有效，${channelsAfterAiFilter.length} 个通过AI筛选，${perfectFitChannels.length} 个 Perfect 匹配，${contentOkChannels.length} 个 Content-OK`,
      )

      const allChannels = validChannelsWithVideos
      if (allChannels.length > 0) {
        await Promise.all([
          this.upsertKolInfo(createdBy, allChannels),
          this.processAndUpsertChannels(allChannels),
        ])

        // create ProjectKol records only for perfect channels with LIKE attitude
        if (perfectChannels.length > 0) {
          await this.createProjectKolRecordsForChannels(
            perfectChannels,
            projectId,
            taskId,
            createdBy,
          )
        }
      }

      return {
        contentOkChannels,
        perfectFitChannels,
      }
    } catch (error) {
      console.error(`[processLongCrawlerBatchChannels] 批量处理频道失败:`, error)
      Sentry.captureException(error)
      return {
        contentOkChannels: [],
        perfectFitChannels: [],
      }
    }
  }

  /**
   * 对频道应用AI筛选
   * @param channels 频道列表
   * @param filters 筛选条件
   * @returns 通过AI筛选的频道结果（YouTubeChannelSimilarityOutput）
   */
  private async applyAiFilterForChannels(
    channels: Channel[],
    filters: YtbLongCrawlerFilters,
  ): Promise<YouTubeChannelSimilarityOutput[]> {
    try {
      const validChannels = channels.filter(
        (channel) => channel.videos && channel.videos.length > 0,
      )

      if (validChannels.length === 0) {
        console.warn('[applyAiFilterForChannels] 没有有效的频道进行AI筛选')
        return []
      }

      console.info(`[applyAiFilterForChannels] 开始对 ${validChannels.length} 个频道进行AI筛选`)

      // 使用Bluebird并发处理所有频道的AI分析
      const aiResults = await Bluebird.map(
        validChannels,
        async (channel) => {
          try {
            const result = await analyzeVisualSimilarityService.analyzeYouTubeChannelSimilarity(
              channel,
              filters.kolDescription,
            )
            return result
          } catch (error) {
            console.error(`[applyAiFilterForChannels] 分析频道 ${channel.channelId} 失败:`, error)
            return {
              username: channel.channelId,
              score: 0,
              reason: 'AI分析失败',
              videoIds: [],
            }
          }
        },
        { concurrency: 20 },
      )

      const passedResults = aiResults.filter((result) => result.score === 100)

      console.info(
        `[applyAiFilterForChannels] AI筛选结果: ${validChannels.length} 个频道中有 ${passedResults.length} 个通过筛选`,
      )

      return passedResults
    } catch (error) {
      console.error(`[applyAiFilterForChannels] AI筛选失败:`, error)
      return []
    }
  }

  /**
   * 对频道应用人口统计学筛选
   * @param channels 频道列表
   * @param filters 筛选条件
   * @returns 通过人口统计学筛选的频道列表
   */
  private applyDemographicFiltersForChannels(
    channels: Channel[],
    filters: YtbLongCrawlerFilters,
  ): Channel[] {
    return channels.filter((channel) => {
      // 订阅数筛选
      if (filters.followerRange) {
        const subscriberCount = channel.subscriberCount || 0
        if (filters.followerRange.min && subscriberCount < filters.followerRange.min) {
          return false
        }
        if (filters.followerRange.max && subscriberCount > filters.followerRange.max) {
          return false
        }
      }

      // 平均播放量筛选
      if (filters.averagePlay) {
        const averageViewCount = channel.videosAverageViewCount || 0
        if (filters.averagePlay.min && averageViewCount < filters.averagePlay.min) {
          return false
        }
        if (filters.averagePlay.max && averageViewCount > filters.averagePlay.max) {
          return false
        }
      }

      if (filters.regions && filters.regions.length > 0) {
        const channelCountry = channel.country?.toLowerCase()
        const targetRegions = filters.regions.map((region) => region.toLowerCase())
        if (channelCountry && !targetRegions.includes(channelCountry)) {
          return false
        }
      }

      return true
    })
  }

  /**
   * 为匹配频道创建或更新 ProjectKol 记录
   * @param matchedChannels 匹配的频道列表
   * @param projectId 项目ID
   * @param taskId 任务ID
   * @param createdBy 创建者ID
   */
  private async createProjectKolRecordsForChannels(
    matchedChannels: Channel[],
    projectId: string,
    taskId: string,
    createdBy: string,
  ): Promise<void> {
    try {
      // 获取频道对应的KOL信息
      const channelIds = matchedChannels.map((channel) => channel.channelId)
      const kolInfos = await prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.YOUTUBE,
          platformAccount: {
            in: channelIds,
          },
        },
      })

      const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))

      // 创建ProjectKol记录
      const projectKolData = matchedChannels
        .map((channel) => {
          const kolInfo = kolInfoMap.get(channel.channelId)
          if (!kolInfo) {
            console.warn(
              `[createProjectKolRecordsForChannels] 找不到频道 ${channel.channelId} 的KOL信息`,
            )
            return null
          }

          return {
            projectId,
            kolId: kolInfo.id,
            similarTaskId: taskId,
            attitude: ProjectKolAttitude.LIKE,
            rateBy: createdBy,
          }
        })
        .filter((data): data is NonNullable<typeof data> => data !== null)

      if (projectKolData.length > 0) {
        await prisma.projectKol.createMany({
          data: projectKolData,
          skipDuplicates: true,
        })
        console.info(
          `[createProjectKolRecordsForChannels] 创建了 ${projectKolData.length} 个ProjectKol记录`,
        )
      }
    } catch (error) {
      console.error(`[createProjectKolRecordsForChannels] 创建ProjectKol记录失败:`, error)
      Sentry.captureException(error)
    }
  }

  /**
   * 获取长时间爬取任务的用户数据
   */
  public async getLongCrawlerUsers(
    taskId: string,
    userId: string,
    page: number = 1,
    pageSize: number = 100,
    type: 'perfectFit' | 'contentOk',
    attitude?: string,
  ): Promise<{
    data: any[]
    total: number
    page: number
    pageSize: number
    totalPages: number
    hasMore: boolean
    attitude: {
      like: number
      dislike: number
      superlike: number
      norate: number
    }
  }> {
    // 验证 attitude 参数
    if (attitude && !Object.values(ProjectKolAttitude).includes(attitude as ProjectKolAttitude)) {
      throwError(
        StatusCodes.BAD_REQUEST,
        `Invalid attitude parameter: ${attitude}. Valid values are: ${Object.values(ProjectKolAttitude).join(', ')}`,
      )
    }

    // 获取任务信息并验证权限
    const task = await prisma.similarChannelTask.findUniqueOrThrow({
      where: { id: taskId },
    })

    if (task.createdBy !== userId) {
      throwError(StatusCodes.FORBIDDEN, 'no permission to access this task')
    }

    // 从任务的 candidate 字段获取用户数据
    const candidateData = task.candidate as any
    const contentOkUsers: YoutubePriorityUser[] = candidateData?.contentOkUsers || []
    const perfectFitUsers: YoutubePriorityUser[] = candidateData?.perfectFitUsers || []

    // 根据 type 参数选择对应的用户数据
    const targetUsers = type === 'perfectFit' ? perfectFitUsers : contentOkUsers
    const targetChannelIds = targetUsers.map((user) => user.username) // username实际是channelId

    if (!targetChannelIds.length) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
        hasMore: false,
        attitude: {
          like: 0,
          dislike: 0,
          superlike: 0,
          norate: 0,
        },
      }
    }

    // 并行查询目标频道的 ProjectKol 记录（用于过滤和统计）
    const allProjectKolsPromise = prisma.projectKol.findMany({
      where: {
        projectId: task.projectId,
        KolInfo: {
          platformAccount: { in: targetChannelIds },
          platform: KolPlatform.YOUTUBE,
        },
      },
      select: {
        attitude: true,
        KolInfo: {
          select: {
            platformAccount: true,
          },
        },
      },
    })

    const allProjectKols = await allProjectKolsPromise

    const attitudeMap = new Map<string, ProjectKolAttitude>()
    allProjectKols.forEach((pk) => {
      if (pk.KolInfo?.platformAccount) {
        attitudeMap.set(pk.KolInfo.platformAccount, pk.attitude)
      }
    })

    // 从候选数据中获取reason信息
    const reasonMap = new Map<string, string>()

    // 从contentOkUsers和perfectFitUsers中获取reason信息
    contentOkUsers.forEach((user) => {
      if (user.reason) {
        reasonMap.set(user.username, user.reason)
      }
    })

    perfectFitUsers.forEach((user) => {
      if (user.reason) {
        reasonMap.set(user.username, user.reason)
      }
    })

    let filteredChannelIds = targetChannelIds
    if (attitude) {
      filteredChannelIds = targetChannelIds.filter(
        (channelId: string) => attitudeMap.get(channelId) === (attitude as ProjectKolAttitude),
      )
    }

    // 计算分页
    const total = filteredChannelIds.length
    const totalPages = Math.ceil(total / pageSize)
    const offset = (page - 1) * pageSize
    const paginatedChannelIds = filteredChannelIds.slice(offset, offset + pageSize)

    // 查询频道完整信息
    const kols = await prisma.kolInfo.findMany({
      where: {
        platformAccount: { in: paginatedChannelIds },
        platform: KolPlatform.YOUTUBE,
      },
      include: {
        youtubeChannel: {
          select: {
            channelId: true,
            channelName: true,
            channelHandle: true,
            channelDescription: true,
            numericSubscriberCount: true,
            country: true,
            videosAverageViewCount: true,
            lastPublishedTime: true,
            createdAt: true,
            updatedAt: true,
            videos: true,
            publicationStats: true,
          },
        },
        ProjectKol: {
          where: {
            projectId: task.projectId,
            similarTaskId: taskId,
          },
          select: {
            attitude: true,
          },
          take: 1,
        },
      },
    })

    console.log(
      `[getLongCrawlerUsers] 查询到 ${kols.length} 个${type}频道，其中有 youtubeChannel 的频道数量: ${kols.filter((k) => k.youtubeChannel).length}`,
    )

    // 处理频道数据，补充视频信息
    const videosUpdatePromises: Promise<any>[] = []

    const kolPromises = kols.map(async (kol: any) => {
      if (kol.youtubeChannel) {
        let videos: any[] = []

        const videosData = kol.youtubeChannel.videos
        const isValidVideos =
          videosData &&
          (Array.isArray(videosData) ||
            (typeof videosData === 'object' && Array.isArray(videosData.videos)))

        if (isValidVideos) {
          const actualVideos = Array.isArray(videosData) ? videosData : videosData.videos

          if (actualVideos && actualVideos.length > 0) {
            videos = actualVideos.map((video: any) => ({
              ...video,
              videoId: video.videoId ? String(video.videoId) : video.videoId,
              viewCount: video.viewCount ? String(video.viewCount) : '0',
              publishedAt: video.publishedAt ? String(video.publishedAt) : '',
            }))
          } else {
            console.log(
              `[getLongCrawlerUsers] 频道 ${kol.youtubeChannel.channelId} 数据库中的 videos 为空，将调用 API`,
            )
          }
        }

        // 如果没有有效的 videos 数据，则调用 API
        if (videos.length === 0) {
          try {
            console.log(
              `[getLongCrawlerUsers] 频道 ${kol.youtubeChannel.channelId} 调用 API 获取视频`,
            )
            const channelVideos = await YoutubeApi.getInstance().getChannelVideos(
              kol.youtubeChannel.channelId,
            )
            videos =
              channelVideos?.map((video: any) => ({
                ...video,
                videoId: video.videoId ? String(video.videoId) : video.videoId,
                viewCount: video.viewCount ? String(video.viewCount) : '0',
                publishedAt: video.publishedAt ? String(video.publishedAt) : '',
              })) || []

            // 收集数据库更新操作，稍后批量执行
            if (videos.length > 0) {
              videosUpdatePromises.push(
                prisma.youTubeChannel.update({
                  where: { channelId: kol.youtubeChannel.channelId },
                  data: { videos: videos },
                }),
              )
            }
          } catch (error) {
            console.error(
              `Failed to get videos for channel ${kol.youtubeChannel.channelId}:`,
              error,
            )
            videos = []
          }
        }

        return {
          platformAccount: kol.platformAccount,
          kol: {
            ...kol,
            ProjectKol: kol.ProjectKol && kol.ProjectKol.length > 0 ? kol.ProjectKol[0] : null,
            youtubeChannel: {
              ...kol.youtubeChannel,
              numericSubscriberCount: Number(kol.youtubeChannel.numericSubscriberCount),
              videosAverageViewCount: Number(kol.youtubeChannel.videosAverageViewCount),
              lastPublishedTime: Number(kol.youtubeChannel.lastPublishedTime),
              videos: videos.sort(
                (a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
              ),
            },
          },
        }
      }
      return null
    })

    // 并行执行频道数据处理和数据库更新
    const [processedKols] = await Promise.all([
      Promise.all(kolPromises),
      Promise.all(videosUpdatePromises).catch((error) => {
        console.error('Failed to update some videos:', error)
      }),
    ])
    const filteredKols = processedKols.filter((kol): kol is NonNullable<typeof kol> => kol !== null)
    const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
      acc[platformAccount] = kol
      return acc
    }, {} as any)

    const result = paginatedChannelIds
      .filter((channelId: string) => channelId in kolMap)
      .map((channelId: string) => ({
        ...kolMap[channelId],
        reason: reasonMap.get(channelId) || '', // 添加reason字段
      }))

    // 计算 attitude 统计（复用之前的查询结果）
    const attitudeStats = {
      like: 0,
      dislike: 0,
      superlike: 0,
      norate: 0,
    }

    // 统计已评价的频道
    allProjectKols.forEach((projectKol) => {
      switch (projectKol.attitude) {
        case ProjectKolAttitude.LIKE:
          attitudeStats.like++
          break
        case ProjectKolAttitude.DISLIKE:
          attitudeStats.dislike++
          break
        case ProjectKolAttitude.SUPERLIKE:
          attitudeStats.superlike++
          break
        case ProjectKolAttitude.NORATE:
          attitudeStats.norate++
          break
      }
    })

    // 计算未评价的频道数量（目标频道数 - 已评价频道数）
    const ratedChannelsCount = allProjectKols.length
    const norateCount = targetChannelIds.length - ratedChannelsCount
    attitudeStats.norate += norateCount

    return {
      data: result,
      total,
      page,
      pageSize,
      totalPages,
      hasMore: page < totalPages,
      attitude: attitudeStats,
    }
  }

  /**
   * 获取最新5个perfect-fit和content-ok的数据
   */
  public async getLatestPerfectFitAndContentOkUsers(
    taskId: string,
    userId: string,
  ): Promise<{
    perfectFitUsers: any[]
    contentOkUsers: any[]
  }> {
    try {
      // 获取任务信息并验证权限
      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      if (task.createdBy !== userId) {
        return {
          perfectFitUsers: [],
          contentOkUsers: [],
        }
      }

      // 从任务的 candidate 字段获取用户数据
      const candidateData = task.candidate as any
      const perfectFitUsers: YoutubePriorityUser[] = candidateData?.perfectFitUsers || []
      const contentOkUsers: YoutubePriorityUser[] = candidateData?.contentOkUsers || []

      if (!perfectFitUsers.length && !contentOkUsers.length) {
        return {
          perfectFitUsers: [],
          contentOkUsers: [],
        }
      }

      // 获取最新5个频道
      const latestPerfectFit = perfectFitUsers.slice(-5).map((user) => user.username)
      const latestContentOk = contentOkUsers.slice(-5).map((user) => user.username)

      const allChannelIds = [...latestPerfectFit, ...latestContentOk]

      // 查询频道信息
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: allChannelIds },
          platform: KolPlatform.YOUTUBE,
        },
        include: {
          youtubeChannel: {
            select: {
              channelId: true,
              channelName: true,
              channelHandle: true,
              channelDescription: true,
              numericSubscriberCount: true,
              country: true,
              videosAverageViewCount: true,
              lastPublishedTime: true,
              createdAt: true,
              updatedAt: true,
              videos: true,
              publicationStats: true,
            },
          },
          ProjectKol: {
            where: {
              projectId: task.projectId,
              similarTaskId: taskId,
            },
            select: {
              attitude: true,
            },
            take: 1,
          },
        },
      })

      console.log(
        `[getLatestPerfectFitAndContentOkUsers] 查询到 ${kols.length} 个频道，其中有 youtubeChannel 的频道数量: ${kols.filter((k) => k.youtubeChannel).length}`,
      )

      // 处理频道数据，补充视频信息
      const videosUpdatePromises: Promise<any>[] = []

      const processChannelData = async (
        channelIds: string[],
        userType: 'perfect' | 'content-ok',
      ) => {
        const channelPromises = channelIds.map(async (channelId) => {
          const kol = kols.find((k) => k.platformAccount === channelId)
          if (!kol) return null

          if (kol.youtubeChannel) {
            let videos: any[] = []

            const videosData = kol.youtubeChannel.videos
            const isValidVideos =
              videosData &&
              (Array.isArray(videosData) ||
                (typeof videosData === 'object' && Array.isArray(videosData.videos)))

            if (isValidVideos) {
              const actualVideos = Array.isArray(videosData)
                ? videosData
                : (videosData as any).videos

              if (actualVideos && Array.isArray(actualVideos) && actualVideos.length > 0) {
                videos = actualVideos.map((video: any) => ({
                  ...video,
                  videoId: video.videoId ? String(video.videoId) : video.videoId,
                  viewCount: video.viewCount ? String(video.viewCount) : '0',
                  publishedAt: video.publishedAt ? String(video.publishedAt) : '',
                }))
              } else {
                console.log(
                  `[getLatestPerfectFitAndContentOkUsers] 频道 ${kol.youtubeChannel.channelId} 数据库中的 videos 为空，将调用 API`,
                )
              }
            }

            // 如果没有有效的 videos 数据，则调用 API
            if (videos.length === 0) {
              try {
                console.log(
                  `[getLatestPerfectFitAndContentOkUsers] 频道 ${kol.youtubeChannel.channelId} 调用 API 获取视频`,
                )
                const channelVideos = await YoutubeApi.getInstance().getChannelVideos(
                  kol.youtubeChannel.channelId,
                )
                videos =
                  channelVideos?.map((video: any) => ({
                    ...video,
                    videoId: video.videoId ? String(video.videoId) : video.videoId,
                    viewCount: video.viewCount ? String(video.viewCount) : '0',
                    publishedAt: video.publishedAt ? String(video.publishedAt) : '',
                  })) || []

                // 收集数据库更新操作，稍后批量执行
                if (videos.length > 0) {
                  videosUpdatePromises.push(
                    prisma.youTubeChannel.update({
                      where: { channelId: kol.youtubeChannel.channelId },
                      data: { videos: videos },
                    }),
                  )
                }
              } catch (error) {
                console.error(
                  `Failed to get videos for channel ${kol.youtubeChannel.channelId}:`,
                  error,
                )
                videos = []
              }
            }

            const projectKol = kol.ProjectKol?.[0]
            const targetUser = (userType === 'perfect' ? perfectFitUsers : contentOkUsers).find(
              (user) => user.username === channelId,
            )

            return {
              ...kol,
              ProjectKol: projectKol || null,
              reason: targetUser?.reason || '',
              videoIds: targetUser?.videoIds || [],
              youtubeChannel: {
                ...kol.youtubeChannel,
                numericSubscriberCount: Number(kol.youtubeChannel.numericSubscriberCount),
                videosAverageViewCount: Number(kol.youtubeChannel.videosAverageViewCount),
                lastPublishedTime: Number(kol.youtubeChannel.lastPublishedTime),
                videos: videos.sort(
                  (a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
                ),
              },
            }
          }
          return null
        })

        const processedChannels = await Promise.all(channelPromises)
        return processedChannels.filter((item): item is NonNullable<typeof item> => item !== null)
      }

      // 并行处理数据和更新数据库
      const [perfectFitResult, contentOkResult] = await Promise.all([
        processChannelData(latestPerfectFit, 'perfect'),
        processChannelData(latestContentOk, 'content-ok'),
        Promise.all(videosUpdatePromises).catch((error) => {
          console.error('Failed to update some videos:', error)
        }),
      ])

      return {
        perfectFitUsers: perfectFitResult,
        contentOkUsers: contentOkResult,
      }
    } catch (error) {
      console.error(`[getLatestPerfectFitAndContentOkUsers] 获取最新用户数据失败:`, error)
      return {
        perfectFitUsers: [],
        contentOkUsers: [],
      }
    }
  }
}

export default YoutubeService

export type ChannelWithCount = BaseChannel & {
  count: number
  viewCount: number
}
