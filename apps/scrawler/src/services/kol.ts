import RapidApiStatsCollector from '@/api/ApiCallStat'
import InstagramApi from '@/api/instagram'
import TiktokApi from '@/api/tiktok'
import { logTime } from '@/api/util'
import YoutubeApi from '@/api/youtube'
import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import Sentry from '@/infras/sentry'
import { GetKolIdResponse } from '@/routes/schemas/kol'
import EmailService from '@/services/email.ts'
import InstagramService from '@/services/instagram.ts'
import { addEmailForKol, EmailFields, findKolByIdentifier } from '@/services/kolInfo.service'
import TiktokService from '@/services/tiktok.ts'
import YoutubeService from '@/services/youtube.ts'
import { EmailSourceType } from '@/types/email'
import { ttSocialMediaIds } from '@/types/kol'
import { acquire, release } from '@/utils/lock'
import { KolInfo, KolPlatform, prisma } from '@repo/database'
import Bluebird from 'bluebird'
import dayjs from 'dayjs'
import { scheduler } from 'timers/promises'
import { ContactItem, ContactType, ParseableExternalLink } from './contact/contact'
import { newExternalLink } from './contact/contact.utils'

class KolService {
  private static instance: KolService

  static getInstance() {
    if (!KolService.instance) {
      KolService.instance = new KolService()
    }
    return KolService.instance
  }

  async getKolByPlatformAndId(id: string, platform: KolPlatform): Promise<KolInfo | null> {
    switch (platform) {
      case 'YOUTUBE':
      case 'TIKTOK':
      case 'INSTAGRAM':
        return prisma.kolInfo.findFirst({
          where: {
            platform: platform,
            platformAccount: id,
          },
        })
      default:
        throw new Error('Unsupported platform' + platform)
    }
  }

  async updateTikTokHandler(handler: string) {
    handler = handler.startsWith('@') ? handler : `@${handler}`
    const cleanHandler = handler.substring(1)

    const ttUserInfo = await prisma.tikTokUserInfo.findFirst({
      where: {
        uniqueId: cleanHandler,
      },
    })

    if (ttUserInfo) {
      const ttUserDetail = await TiktokApi.getInstance().getUserDetail({
        user_id: ttUserInfo.userId,
      })

      if (ttUserDetail?.user) {
        await Promise.all([
          prisma.kolInfo.updateMany({
            where: {
              platform: KolPlatform.TIKTOK,
              platformAccount: cleanHandler,
            },
            data: {
              platformAccount: ttUserDetail.user.uniqueId,
            },
          }),
          prisma.tikTokUserInfo.update({
            where: {
              userId: ttUserInfo.userId,
            },
            data: {
              uniqueId: ttUserDetail.user.uniqueId,
            },
          }),
        ])
      }
    }
  }

  async createOrUpdateKolInfo(
    userId: string | undefined,
    platform: KolPlatform,
    identifier: string,
  ): Promise<KolInfo | null> {
    switch (platform) {
      case KolPlatform.TIKTOK:
        return this.createTikTokKolInfo(userId, identifier)
      case KolPlatform.YOUTUBE:
        return (
          await YoutubeService.getInstance().createKolInfosForChannels(userId, [identifier])
        )[0]
      case KolPlatform.INSTAGRAM:
        return await InstagramService.getInstance().createKolById(userId, identifier)
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }
  }

  private async createTikTokKolInfo(
    userId: string | undefined,
    uniqueId: string,
  ): Promise<KolInfo | null> {
    try {
      if (!uniqueId) {
        throw new Error('TikTok uniqueId is required')
      }

      const tiktokUsers = await TiktokApi.getInstance().getUserDetail({
        unique_id: uniqueId,
      })

      if (!tiktokUsers?.user) {
        console.error('TikTok user info not found:', uniqueId)
        return null
      }

      const kolData = {
        title: tiktokUsers.user.nickname,
        description: tiktokUsers.user.signature,
        email: tiktokUsers.user.email,
        emailSource: tiktokUsers.user.emailSource,
        updatedAt: new Date(),
      }

      const existingKol = await prisma.kolInfo.findFirst({
        where: {
          platform: KolPlatform.TIKTOK,
          platformAccount: tiktokUsers.user.uniqueId,
        },
      })

      if (existingKol) {
        const emailFields: EmailFields = {
          email: undefined,
          emailSource: undefined,
          emailUpdatedAt: undefined,
          historyEmails: undefined,
        }
        const needUpdateEmail =
          tiktokUsers.user?.email && existingKol.email !== tiktokUsers.user.email
        if (needUpdateEmail) {
          emailFields.email = tiktokUsers.user.email
          emailFields.emailSource = tiktokUsers.user.emailSource as EmailSourceType
          emailFields.emailUpdatedAt = new Date()
          emailFields.historyEmails = [
            ...new Set([...existingKol.historyEmails, tiktokUsers.user.email as string]),
          ]
        }

        return prisma.kolInfo.update({
          where: { id: existingKol.id },
          data: {
            ...kolData,
            ...emailFields,
            historyEmails: emailFields.historyEmails ?? existingKol.historyEmails,
          },
        })
      }

      return prisma.kolInfo.create({
        data: {
          ...kolData,
          historyEmails: tiktokUsers.user.email ? [tiktokUsers.user.email] : [],
          platformAccount: tiktokUsers.user.uniqueId,
          platform: KolPlatform.TIKTOK,
        },
      })
    } catch (error) {
      Sentry.captureException(error)
      console.error('Error in createTikTokKolInfo:', error)
      return null
    }
  }

  async findOrCreateKol(
    userId: string | undefined,
    handler: string,
    id: string,
    platform: KolPlatform,
  ): Promise<KolInfo | null> {
    let kol: KolInfo | null = null
    kol = await findKolByIdentifier({ id, handler }, platform)
    if (!kol || dayjs(kol.updatedAt).isBefore(dayjs().subtract(30, 'day'))) {
      const identifier = handler || id
      kol = await this.createOrUpdateKolInfo(userId, platform, identifier)
    }
    return kol
  }

  getFulfillEmailInfoTaskKey(projectId: string) {
    return `fulfillEmailInfo:${projectId}`
  }

  // 填充邮箱信息
  async fulfillEmailInfo(
    userId: string | undefined,
    ids: string[],
    platform: KolPlatform,
    projectId: string,
  ) {
    const projectKey = this.getFulfillEmailInfoTaskKey(projectId)
    await acquire(projectKey, 60)

    let fulfilledCount = 0

    console.log(`fulfillEmailInfo for ${ids.length} kols, ${projectId}`)

    const kolInfoWithoutEmail = await prisma.kolInfo.findMany({
      where: {
        platformAccount: {
          in: ids,
        },
        platform,
        OR: [{ email: null }, { email: '' }],
      },
    })

    // const platformAccounts = platform === KolPlatform.YOUTUBE ?
    //   kolInfoWithoutEmail.map((kol) => kol.platformAccount?.replace('@', '')!) :
    //   kolInfoWithoutEmail.map((kol) => kol.platformAccount!).concat(kolInfoWithoutEmail.map((kol) => `@${kol.platformAccount}`))

    // const crossPlatformKolInfoWithEmail = await prisma.kolInfo.findMany({
    //   where: {
    //     platformAccount: {
    //       in: platformAccounts,
    //     },
    //     AND: [{ NOT: { email: null } }, { NOT: { email: '' } }]
    //   },
    // })

    let kolsWithoutEmailAfterPatch = kolInfoWithoutEmail
    // for (const kol of kolInfoWithoutEmail) {
    //   const crossPlatformKol = platform === KolPlatform.YOUTUBE ?
    //     crossPlatformKolInfoWithEmail.find((k) => `@${k.platformAccount}` === kol.platformAccount && k.email) :
    //     crossPlatformKolInfoWithEmail.find((k) => (k.platformAccount === kol.platformAccount || k.platformAccount?.replace('@', '') === kol.platformAccount) && k.email)
    //   if (crossPlatformKol) {
    //     await addEmailForKol(kol.id, crossPlatformKol.email!)
    //     fulfilledCount += 1
    //   } else {
    //     kolsWithoutEmailAfterPatch.push(kol)
    //   }
    // }

    console.log(`found ${kolsWithoutEmailAfterPatch.length} kols without email after patch`)

    let retry: KolInfo[] = []
    let retryCount = 0

    const start = Date.now()
    while (retryCount < 3 && kolsWithoutEmailAfterPatch.length > 0) {
      await Bluebird.map(
        kolsWithoutEmailAfterPatch,
        async (kol) => {
          const success = await this.tryPatchKolEmail(userId, kol as KolInfo)
          if (!success) {
            retry.push(kol as KolInfo)
          } else {
            fulfilledCount += 1
          }
        },
        { concurrency: 8 },
      )
      kolsWithoutEmailAfterPatch = retry
      retryCount++
      retry = []
      console.log(
        `retryCount: ${retryCount}, size: ${kolsWithoutEmailAfterPatch.length}, time: ${Date.now() - start}ms`,
      )
      await scheduler.wait(3000 * retryCount)
    }
    await release(projectKey)
    console.log(`[fulfillEmailInfo] fulfilled ${fulfilledCount}/${ids.length} emails`)
  }

  public async tryPatchKolEmail(userId: string | undefined, kol: KolInfo) {
    if (kol && !kol.email) {
      const newEmail = await EmailService.getInstance().getKolEmail(userId, kol)
      if (newEmail.email) {
        await addEmailForKol(userId, kol.id, newEmail.email, newEmail.source)
        kol.email = newEmail.email
        return true
      }
    }
    return false
  }
  async getPlatformSpecificInfo(kol: KolInfo, platform: KolPlatform, handler?: string) {
    let hasEmail = false
    let socialMediaIds: ttSocialMediaIds[] = []
    let region: string = ''

    switch (platform) {
      case KolPlatform.YOUTUBE: {
        if (!kol.platformAccount) {
          throw new Error('YouTube channel ID is required')
        }

        const youtubeChannel = await prisma.youTubeChannel.findFirst({
          where: {
            channelId: kol.platformAccount,
          },
        })

        hasEmail = youtubeChannel?.officialEmail || false
        region = youtubeChannel?.country ?? ''

        if (youtubeChannel?.channelId && !region) {
          try {
            const channel = await YoutubeApi.getInstance().getChannel(youtubeChannel.channelId)
            region = channel.country ?? ''
            if (region) {
              await prisma.youTubeChannel.update({
                where: {
                  channelId: youtubeChannel.channelId,
                },
                data: {
                  country: region,
                },
              })
            }
          } catch (error: unknown) {
            const err = error as Error
            console.error(`Failed to fetch YouTube channel info: ${youtubeChannel.channelId}`, err)
            Sentry.captureException(err)
          }
        }
        break
      }
      case KolPlatform.TIKTOK: {
        const account = kol.platformAccount || handler
        if (!account) {
          throw new Error('Either platformAccount or handler must be provided for TikTok')
        }
        try {
          const platformInfo = await this.fetchTikTokPlatformInfo(account)
          region = platformInfo.region
          socialMediaIds = platformInfo.socialMediaIds
        } catch (error: unknown) {
          const err = error as Error
          console.error(`Failed to fetch TikTok platform info: ${account}`, err)
          Sentry.captureException(err)
          throw new Error(`Failed to fetch TikTok platform info: ${err.message}`)
        }
        break
      }
      case KolPlatform.INSTAGRAM: {
        if (!kol.platformAccount) {
          throw new Error('Instagram username is required')
        }

        try {
          const user = await prisma.instagramUserInfo.findFirst({
            where: {
              username: handler || kol.platformAccount,
            },
          })

          if (user) {
            region = user.region ?? ''
            if (!user.region) {
              const remoteUser = await InstagramApi.getInstance().getUser(user.username)
              region = remoteUser?.region ?? ''
              if (region) {
                await prisma.instagramUserInfo.update({
                  where: { id: user.id },
                  data: { region },
                })
              }
            }
          } else {
            // 如果用户不存在，调用API获取用户信息并保存
            const remoteUser = await InstagramApi.getInstance().getUserWithPosts(
              kol.platformAccount,
            )
            if (remoteUser) {
              await InstagramService.getInstance().upsertUsers([remoteUser])
              region = remoteUser.region ?? ''
            }
          }
        } catch (error: unknown) {
          const err = error as Error
          console.error(`Failed to fetch Instagram user info: ${kol.platformAccount}`, err)
          Sentry.captureException(err)
          throw new Error(`Failed to fetch Instagram user info: ${err.message}`)
        }
        break
      }
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    return { hasEmail, socialMediaIds, region }
  }

  async fetchTikTokPlatformInfo(uniqueId: string) {
    let region = ''
    let socialMediaIds: ttSocialMediaIds[] = []

    try {
      const userDataInfo = await prisma.tikTokUserInfo.findFirst({
        where: { uniqueId },
      })
      region = userDataInfo?.region || ''

      if (!region) {
        region = await TiktokService.getInstance().getTikTokUserRegion(uniqueId)
      }

      const userInfo = await TiktokApi.getInstance().getUserDetail({ unique_id: uniqueId })
      if (userInfo?.user) {
        const { ins_id, twitter_id, youtube_channel_id, youtube_channel_title } = userInfo.user
        if (ins_id || twitter_id || youtube_channel_id || youtube_channel_title) {
          socialMediaIds = [
            {
              ins_id,
              twitter_id,
              youtube_channel_id,
              youtube_channel_title,
              ins_url: ins_id ? `https://instagram.com/${ins_id}` : '',
              twitter_url: twitter_id ? `https://twitter.com/${twitter_id}` : '',
              youtube_url: youtube_channel_id
                ? `https://youtube.com/channel/${youtube_channel_id}`
                : '',
            },
          ]
        }
      }
    } catch (error) {
      console.error('Failed to fetch TikTok user detail:', error)
      Sentry.captureException(error)
    }

    return { region, socialMediaIds }
  }

  public async updateLinks(
    userId: string | undefined,
    kolId: string,
    links: string[],
  ): Promise<ContactItem[]> {
    if (!kolId?.length) {
      throw new Error('kolId is not provided')
    }

    const existing = await prisma.kolInfo.findUnique({
      where: {
        id: kolId,
      },
      select: {
        contacts: true,
        email: true,
        emailSource: true,
        platform: true,
        platformAccount: true,
      },
    })
    if (!existing) {
      throw new Error('Kol not found')
    }
    links = links.map((link) => {
      if (!link.startsWith('http://') && !link.startsWith('https://')) {
        return 'https://' + link
      }
      return link
    })
    const existContacts = existing.contacts as unknown as ContactItem[]
    const contactMap = new Map<string, ContactItem>()
    // 不要库存
    // (existContacts?.length ? existContacts : []).map((i) => [i.content, i]),
    const results = await Bluebird.map(links, async (link) => {
      const collector = new RapidApiStatsCollector()
      try {
        const linkResult = await newExternalLink(link, link)
        linkResult.setKol(existing as KolInfo)
        if ('parse' in linkResult) {
          const results = await logTime(
            (linkResult as ParseableExternalLink).parse(collector),
            `[sj: external-link]${link}`,
          )
          console.log(
            `[external-link]fetch ${kolId} ${link} with ${collector.getStats().totalCount} api requests, get ${results.length} results}`,
          )
          return results
        }
        return [] as ContactItem[]
      } catch (err) {
        console.log(`failed to extract contact from link: ${link}: ${err}`)
        return [] as ContactItem[]
      }
    })
    results.flat().forEach((i) => {
      if (contactMap.has(i.content)) {
        contactMap.set(i.content, { ...contactMap.get(i.content)!, updatedAt: dayjs().unix() })
      } else {
        contactMap.set(i.content, { updatedAt: dayjs().unix(), ...i })
      }
    })
    let allContacts = Array.from(contactMap.values())
    // filter out current kol info
    allContacts = allContacts.filter((i) => {
      if (i.linkType.toLowerCase() === existing.platform.toLowerCase()) {
        switch (existing.platform) {
          case KolPlatform.YOUTUBE:
            return existing.platformAccount !== i.platformAccount
          case KolPlatform.TIKTOK:
            return existing.platformAccount !== i.platformAccount
          case KolPlatform.INSTAGRAM:
            return existing.platformAccount !== i.platformAccount
          default:
            return true
        }
      }
      return true
    })
    let email: string | undefined = undefined
    let emailSource: EmailSourceType | undefined = undefined
    // 在博主没有邮箱的时候，选取其一填充邮箱
    if (!existing.email) {
      const trustEmails = allContacts.filter((i) => i.type == ContactType.EMAIL && i.emailSource)
      if (trustEmails.length) {
        console.log(`use ${JSON.stringify(trustEmails[0])} to fill kol ${kolId} email.`)
        email = trustEmails[0].content
        emailSource = trustEmails[0].emailSource
      }
    }

    EmailService.getInstance().addEmailAuditLog(userId, kolId, email, emailSource)
    await prisma.kolInfo.update({
      where: {
        id: kolId,
      },
      data: {
        links: links,
        contacts: allContacts,
        email: email,
        emailSource: emailSource,
        emailUpdatedAt: new Date(),
      },
    })
    const uniqueMap = new Map<string, ContactItem>()
    allContacts.forEach((item) => {
      let key = item.content.replace(/[ \t]/g, '')
      if (item.type == ContactType.PHONE) {
        key = Array.from(item.content.match(/[0-9]/g)!).join('')
      }
      uniqueMap.set(key, item)
    })
    return Array.from(uniqueMap.values())
  }

  public async getKolId(platform: KolPlatform, platformAccount: string): Promise<GetKolIdResponse> {
    // 先清洗 id
    platformAccount = platformAccount.split('?')[0]
    platformAccount = platformAccount.split('#')[0]
    // youtube 且是 channel handle，先找库存，没有再请求接口得到 channelId
    if (platform == KolPlatform.YOUTUBE && !platformAccount.startsWith('UC')) {
      if (!platformAccount.startsWith('@')) {
        platformAccount = `@${platformAccount}`
      }
      const channelId = await YoutubeApi.getInstance().getYoutubeChannelId(platformAccount)
      if (!channelId || !channelId.startsWith('UC')) {
        const existChannel = await prisma.youTubeChannel.findFirst({
          where: {
            channelHandle: platformAccount,
          },
          select: {
            kols: {
              select: {
                id: true,
              },
            },
          },
        })
        if (existChannel?.kols.length) {
          return {
            kolId: existChannel.kols[0].id,
            isNew: false,
          }
        }
        Sentry.captureMessage('note request kol channel id', {
          level: 'info',
          extra: {
            platform,
            platformAccount,
            success: false,
          },
        })
        throwError(StatusCodes.BAD_REQUEST, `Cannot find youtube Channel for ${platformAccount}`)
      }
      Sentry.captureMessage('note request kol channel id', {
        level: 'info',
        extra: {
          platform,
          platformAccount,
          success: true,
        },
      })
      platformAccount = channelId as string
    }
    // 库存中找 kol，没有则创建空白博主记录
    const exist = await prisma.kolInfo.findFirst({
      where: {
        platform,
        platformAccount,
      },
    })
    if (exist) {
      return { kolId: exist.id, isNew: false }
    }
    const newKol = await prisma.kolInfo.create({
      data: {
        platform,
        platformAccount,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })
    await Sentry.captureEvent({
      message: 'Empty Kol Created',
      level: 'info',
      extra: {
        platform,
        platformAccount,
      },
    })
    return { kolId: newKol.id, isNew: true }
  }
}

export default KolService
